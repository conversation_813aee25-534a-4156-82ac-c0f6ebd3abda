{"asset": "STXEUR", "timeframe": 300, "timestamp": 1753230098.9509878, "last_update": "2025-07-23T03:21:38.950987", "indicators": {"asset": "STXEUR", "timeframe": 300, "last_update": "2025-07-23T03:21:38.929956", "candles_count": 199, "timestamps": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "rsi": {"values": [41.18, 36.55, 36.55, 35.39, 37.52, 37.52, 37.52, 36.04, 38.64, 35.53, 45.07, 40.26, 40.26, 44.82, 43.05, 38.18, 30.7, 28.69, 33.4, 37.82, 36.51, 36.51, 32.6, 31.39, 38.73, 35.97, 40.53, 36.35, 32.71, 39.26, 41.31, 41.31, 35.71, 43.9, 52.1, 53.56, 51.86, 53.45, 46.77, 45.25, 42.29, 42.29, 44.4, 41.16, 41.16, 43.55, 50.1, 52.1, 52.1, 59.6, 62.74, 65.61, 66.99, 69.6, 66.76, 63.94, 61.16, 46.07, 41.36, 27.32, 25.96, 31.46, 43.5, 39.74, 49.2, 45.36, 46.49, 45.48, 43.45, 39.65, 38.74, 37.8, 43.67, 45.07, 43.9, 41.57, 44.72, 42.27, 43.93, 45.61, 40.38, 42.17, 45.67, 49.0, 49.0, 42.9, 46.49, 43.54, 45.4, 45.4, 40.73, 46.64, 46.64, 44.91, 47.03, 49.14, 49.14, 49.14, 46.81, 46.81, 52.07, 52.07, 57.0, 57.0, 57.0, 53.56, 50.29, 47.19, 50.48, 58.78, 58.78, 58.78, 54.94, 57.9, 57.9, 60.88, 66.05, 66.05, 66.05, 72.79, 72.79, 74.73, 74.73, 74.73, 76.79, 76.79, 70.14, 72.69, 76.93, 76.93, 76.93, 70.13, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04, 64.04], "current": 64.04, "signal": "neutral"}, "macd": {"macd_line": [-2.78, -2.71, -2.46, -2.32, -2.42, -2.95, -3.5, -3.72, -3.69, -3.71, -3.69, -3.86, -4.03, -3.88, -3.88, -3.68, -3.72, -3.94, -3.84, -3.64, -3.43, -3.55, -3.28, -2.64, -2.02, -1.6, -1.17, -1.13, -1.17, -1.35, -1.48, -1.48, -1.62, -1.71, -1.69, -1.41, -1.1, -0.84, -0.31, 0.27, 0.88, 1.43, 2.0, 2.34, 2.5, 2.52, 1.95, 1.24, -0.44, -1.92, -2.82, -2.85, -3.16, -2.73, -2.68, -2.54, -2.47, -2.55, -2.9, -3.23, -3.52, -3.4, -3.18, -3.05, -3.07, -2.89, -2.88, -2.76, -2.56, -2.68, -2.68, -2.48, -2.13, -1.84, -1.91, -1.79, -1.83, -1.76, -1.69, -1.85, -1.71, -1.58, -1.55, -1.42, -1.23, -1.07, -0.93, -0.89, -0.84, -0.64, -0.47, -0.18, 0.06, 0.24, 0.3, 0.26, 0.15, 0.15, 0.38, 0.55, 0.68, 0.7, 0.78, 0.83, 0.94, 1.18, 1.36, 1.48, 1.79, 2.02, 2.25, 2.41, 2.51, 2.63, 2.7, 2.65, 2.66, 2.8, 2.87, 2.9, 2.8, 2.61, 2.44, 2.27, 2.12, 1.97, 1.84, 1.72, 1.6, 1.49, 1.39, 1.29, 1.2, 1.11, 1.03, 0.95, 0.88, 0.81, 0.75, 0.69, 0.64, 0.59, 0.54, 0.5, 0.46, 0.42, 0.39, 0.36, 0.33, 0.3, 0.28, 0.26, 0.24, 0.22, 0.2, 0.18, 0.16, 0.15, 0.14, 0.13, 0.12, 0.11, 0.1, 0.09, 0.08, 0.07, 0.06, 0.05, 0.04, 0.03, 0.03, 0.03, 0.03, 0.03], "signal_line": [-2.95, -3.1, -3.22, -3.35, -3.49, -3.57, -3.63, -3.64, -3.66, -3.72, -3.74, -3.72, -3.66, -3.64, -3.57, -3.38, -3.11, -2.81, -2.48, -2.21, -2.0, -1.87, -1.79, -1.73, -1.71, -1.71, -1.71, -1.65, -1.54, -1.4, -1.18, -0.89, -0.54, -0.15, 0.28, 0.69, 1.05, 1.34, 1.46, 1.42, 1.05, 0.46, -0.2, -0.73, -1.22, -1.52, -1.75, -1.91, -2.02, -2.13, -2.28, -2.47, -2.68, -2.82, -2.89, -2.92, -2.95, -2.94, -2.93, -2.9, -2.83, -2.8, -2.78, -2.72, -2.6, -2.45, -2.34, -2.23, -2.15, -2.07, -1.99, -1.96, -1.91, -1.84, -1.78, -1.71, -1.61, -1.5, -1.39, -1.29, -1.2, -1.09, -0.97, -0.81, -0.64, -0.46, -0.31, -0.2, -0.13, -0.07, 0.02, 0.13, 0.24, 0.33, 0.42, 0.5, 0.59, 0.71, 0.84, 0.97, 1.13, 1.31, 1.5, 1.68, 1.85, 2.01, 2.15, 2.25, 2.33, 2.42, 2.51, 2.59, 2.63, 2.63, 2.59, 2.53, 2.45, 2.35, 2.25, 2.14, 2.03, 1.92, 1.81, 1.71, 1.61, 1.51, 1.41, 1.32, 1.23, 1.15, 1.07, 0.99, 0.92, 0.85, 0.79, 0.73, 0.68, 0.63, 0.58, 0.54, 0.5, 0.46, 0.42, 0.39, 0.36, 0.33, 0.3, 0.28, 0.26, 0.24, 0.22, 0.2, 0.18, 0.17, 0.16, 0.15, 0.14, 0.13, 0.12, 0.11, 0.1, 0.09, 0.08, 0.07, 0.06, 0.05], "histogram": [-0.74, -0.61, -0.47, -0.51, -0.54, -0.31, -0.25, -0.04, -0.06, -0.22, -0.1, 0.08, 0.23, 0.09, 0.29, 0.74, 1.09, 1.21, 1.31, 1.08, 0.83, 0.52, 0.31, 0.25, 0.09, 0.0, 0.02, 0.24, 0.44, 0.56, 0.87, 1.16, 1.42, 1.58, 1.72, 1.65, 1.45, 1.18, 0.49, -0.18, -1.49, -2.38, -2.62, -2.12, -1.94, -1.21, -0.93, -0.63, -0.45, -0.42, -0.62, -0.76, -0.84, -0.58, -0.29, -0.13, -0.12, 0.05, 0.05, 0.14, 0.27, 0.12, 0.1, 0.24, 0.47, 0.61, 0.43, 0.44, 0.32, 0.31, 0.3, 0.11, 0.2, 0.26, 0.23, 0.29, 0.38, 0.43, 0.46, 0.4, 0.36, 0.45, 0.5, 0.63, 0.7, 0.7, 0.61, 0.46, 0.28, 0.22, 0.36, 0.42, 0.44, 0.37, 0.36, 0.33, 0.35, 0.47, 0.52, 0.51, 0.66, 0.71, 0.75, 0.73, 0.66, 0.62, 0.55, 0.4, 0.33, 0.38, 0.36, 0.31, 0.17, -0.02, -0.15, -0.26, -0.33, -0.38, -0.41, -0.42, -0.43, -0.43, -0.42, -0.42, -0.41, -0.4, -0.38, -0.37, -0.35, -0.34, -0.32, -0.3, -0.28, -0.26, -0.25, -0.23, -0.22, -0.21, -0.19, -0.18, -0.17, -0.16, -0.14, -0.13, -0.12, -0.11, -0.1, -0.1, -0.1, -0.09, -0.08, -0.07, -0.06, -0.06, -0.06, -0.06, -0.06, -0.06, -0.06, -0.06, -0.06, -0.06, -0.05, -0.04, -0.03, -0.02], "current": {"macd": 0.03, "signal": 0.05, "histogram": -0.02}, "trend": "bearish"}, "moving_averages": {"sma_20": {"values": [5312.6, 5312.1, 5311.8, 5311.75, 5311.35, 5311.05, 5310.55, 5309.95, 5309.7, 5309.4, 5308.9, 5308.1, 5307.4, 5306.65, 5306.1, 5305.5, 5305.1, 5304.55, 5304.0, 5303.55, 5303.0, 5302.55, 5302.0, 5301.25, 5300.75, 5300.1, 5299.6, 5298.9, 5298.3, 5298.0, 5297.9, 5298.05, 5298.35, 5298.35, 5298.2, 5298.0, 5297.8, 5297.8, 5297.75, 5297.55, 5297.5, 5297.5, 5297.7, 5298.05, 5298.45, 5298.9, 5299.45, 5300.25, 5300.95, 5301.35, 5301.65, 5301.95, 5301.85, 5301.8, 5301.1, 5300.4, 5299.85, 5299.65, 5299.35, 5299.45, 5299.3, 5299.05, 5298.7, 5298.25, 5297.4, 5296.4, 5295.25, 5294.25, 5293.2, 5292.15, 5291.05, 5290.1, 5289.4, 5288.9, 5289.15, 5289.3, 5289.35, 5289.1, 5289.15, 5288.8, 5288.45, 5288.15, 5287.8, 5287.6, 5287.6, 5287.5, 5287.6, 5287.5, 5287.3, 5287.2, 5287.25, 5287.2, 5287.25, 5287.2, 5287.1, 5287.3, 5287.45, 5287.6, 5287.65, 5287.7, 5287.9, 5287.95, 5288.05, 5288.15, 5288.4, 5288.8, 5289.05, 5289.25, 5289.55, 5289.8, 5290.05, 5290.4, 5290.75, 5291.15, 5291.7, 5292.15, 5292.65, 5293.05, 5293.45, 5293.9, 5294.4, 5294.9, 5295.5, 5296.15, 5296.65, 5297.15, 5297.6, 5298.05, 5298.45, 5298.85, 5299.2, 5299.45, 5299.7, 5299.95, 5300.05, 5300.15, 5300.2, 5300.25, 5300.3, 5300.3, 5300.3, 5300.35, 5300.35, 5300.25, 5300.15, 5300.05, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0], "current": 5300.0}, "ema_20": {"values": [5312.6, 5312.16, 5311.67, 5311.32, 5310.81, 5310.73, 5310.37, 5310.05, 5309.95, 5309.76, 5309.31, 5308.33, 5307.25, 5306.46, 5305.94, 5305.37, 5304.86, 5304.11, 5303.34, 5302.93, 5302.37, 5302.05, 5301.47, 5300.66, 5300.22, 5299.91, 5299.63, 5299.0, 5298.81, 5299.11, 5299.48, 5299.72, 5300.03, 5299.93, 5299.75, 5299.39, 5299.07, 5298.87, 5298.5, 5298.17, 5297.96, 5298.06, 5298.24, 5298.41, 5298.94, 5299.61, 5300.41, 5301.23, 5302.16, 5302.91, 5303.49, 5303.92, 5303.64, 5303.1, 5301.28, 5299.44, 5298.06, 5297.58, 5296.76, 5296.78, 5296.42, 5296.19, 5295.89, 5295.42, 5294.62, 5293.8, 5292.96, 5292.58, 5292.33, 5292.01, 5291.53, 5291.29, 5290.88, 5290.61, 5290.46, 5289.94, 5289.56, 5289.41, 5289.47, 5289.52, 5289.18, 5289.07, 5288.78, 5288.61, 5288.46, 5288.04, 5287.94, 5287.85, 5287.67, 5287.61, 5287.65, 5287.68, 5287.71, 5287.64, 5287.58, 5287.72, 5287.84, 5288.14, 5288.41, 5288.66, 5288.79, 5288.81, 5288.73, 5288.76, 5289.07, 5289.35, 5289.6, 5289.73, 5289.95, 5290.15, 5290.42, 5290.86, 5291.25, 5291.61, 5292.22, 5292.77, 5293.36, 5293.9, 5294.39, 5294.92, 5295.4, 5295.74, 5296.15, 5296.71, 5297.21, 5297.67, 5297.99, 5298.18, 5298.35, 5298.51, 5298.65, 5298.78, 5298.9, 5299.0, 5299.1, 5299.19, 5299.27, 5299.34, 5299.4, 5299.46, 5299.51, 5299.56, 5299.6, 5299.64, 5299.67, 5299.7, 5299.73, 5299.76, 5299.78, 5299.8, 5299.82, 5299.84, 5299.86, 5299.87, 5299.88, 5299.89, 5299.9, 5299.91, 5299.92, 5299.93, 5299.94, 5299.95, 5299.95, 5299.95, 5299.95, 5299.95, 5299.95, 5299.95, 5299.95, 5299.95, 5299.95, 5299.95, 5299.95, 5299.95, 5299.95, 5299.95, 5299.95, 5299.95, 5299.95, 5299.95], "current": 5299.95}, "sma_50": {"values": [5305.7, 5305.38, 5305.18, 5304.98, 5304.66, 5304.26, 5303.84, 5303.4, 5303.02, 5302.64, 5302.26, 5301.94, 5301.72, 5301.44, 5301.28, 5301.16, 5301.16, 5301.18, 5301.26, 5301.3, 5301.32, 5301.32, 5301.2, 5301.0, 5300.56, 5300.0, 5299.56, 5299.28, 5298.88, 5298.66, 5298.42, 5298.32, 5298.24, 5298.08, 5297.8, 5297.52, 5297.22, 5297.06, 5296.94, 5296.74, 5296.54, 5296.34, 5296.16, 5296.06, 5295.92, 5295.68, 5295.46, 5295.36, 5295.22, 5294.98, 5294.64, 5294.36, 5294.02, 5293.78, 5293.56, 5293.32, 5293.14, 5292.94, 5292.76, 5292.6, 5292.44, 5292.22, 5291.98, 5291.72, 5291.38, 5291.04, 5290.66, 5290.3, 5289.9, 5289.52, 5289.14, 5288.76, 5288.5, 5288.32, 5288.48, 5288.68, 5288.82, 5288.78, 5288.84, 5288.74, 5288.74, 5288.76, 5288.8, 5288.88, 5289.1, 5289.34, 5289.62, 5289.82, 5290.0, 5290.22, 5290.48, 5290.68, 5290.94, 5291.22, 5291.48, 5291.82, 5292.12, 5292.36, 5292.56, 5292.76, 5293.04, 5293.28, 5293.56, 5293.82, 5294.08, 5294.4, 5294.66, 5294.92, 5295.2, 5295.46, 5295.7, 5295.94, 5296.18, 5296.44, 5296.7, 5296.92, 5297.14, 5297.32, 5297.5, 5297.68, 5297.88, 5298.1, 5298.34, 5298.56, 5298.72, 5298.88, 5299.04, 5299.22, 5299.38, 5299.54, 5299.68, 5299.78, 5299.88, 5299.98, 5300.02, 5300.06, 5300.08, 5300.1, 5300.12, 5300.12, 5300.12, 5300.14, 5300.14, 5300.1, 5300.06, 5300.02, 5300.0, 5300.0, 5300.0, 5300.0], "current": 5300.0}, "ema_50": {"values": [5305.7, 5305.55, 5305.45, 5305.2, 5304.92, 5304.57, 5304.23, 5303.95, 5303.6, 5303.26, 5302.98, 5302.82, 5302.71, 5302.6, 5302.65, 5302.78, 5302.98, 5303.22, 5303.53, 5303.78, 5303.98, 5304.14, 5304.02, 5303.78, 5303.0, 5302.18, 5301.51, 5301.18, 5300.7, 5300.55, 5300.25, 5300.0, 5299.73, 5299.39, 5298.9, 5298.39, 5297.86, 5297.51, 5297.22, 5296.9, 5296.51, 5296.22, 5295.86, 5295.55, 5295.29, 5294.89, 5294.54, 5294.28, 5294.11, 5293.95, 5293.64, 5293.42, 5293.13, 5292.89, 5292.66, 5292.32, 5292.11, 5291.91, 5291.68, 5291.5, 5291.36, 5291.23, 5291.1, 5290.94, 5290.79, 5290.72, 5290.65, 5290.66, 5290.67, 5290.68, 5290.65, 5290.59, 5290.49, 5290.43, 5290.49, 5290.55, 5290.61, 5290.63, 5290.68, 5290.73, 5290.82, 5290.98, 5291.14, 5291.29, 5291.55, 5291.8, 5292.08, 5292.35, 5292.61, 5292.9, 5293.18, 5293.41, 5293.67, 5294.0, 5294.31, 5294.61, 5294.86, 5295.06, 5295.25, 5295.44, 5295.62, 5295.79, 5295.96, 5296.12, 5296.27, 5296.42, 5296.56, 5296.69, 5296.82, 5296.94, 5297.06, 5297.18, 5297.29, 5297.4, 5297.5, 5297.6, 5297.69, 5297.78, 5297.87, 5297.95, 5298.03, 5298.11, 5298.18, 5298.25, 5298.32, 5298.39, 5298.45, 5298.51, 5298.57, 5298.63, 5298.68, 5298.73, 5298.78, 5298.83, 5298.88, 5298.92, 5298.96, 5299.0, 5299.04, 5299.08, 5299.12, 5299.15, 5299.18, 5299.21, 5299.24, 5299.27, 5299.3, 5299.33, 5299.36, 5299.39], "current": 5299.39}, "trend": "sideways"}, "bollinger_bands": {"upper": [5319.71, 5319.03, 5319.06, 5319.1, 5319.03, 5318.44, 5317.61, 5316.0, 5315.47, 5314.86, 5314.03, 5314.1, 5314.95, 5314.4, 5313.82, 5313.16, 5313.02, 5313.09, 5313.23, 5312.83, 5312.47, 5311.88, 5311.51, 5311.11, 5310.61, 5309.11, 5308.12, 5307.17, 5305.17, 5303.55, 5303.0, 5303.43, 5304.12, 5304.12, 5303.84, 5303.66, 5303.44, 5303.44, 5303.47, 5303.36, 5303.35, 5303.35, 5303.6, 5303.62, 5304.5, 5305.74, 5307.29, 5308.54, 5310.32, 5311.51, 5312.33, 5312.99, 5312.88, 5312.89, 5314.57, 5316.13, 5316.87, 5316.89, 5317.11, 5317.13, 5317.14, 5317.04, 5316.88, 5316.72, 5316.29, 5315.48, 5314.17, 5312.25, 5309.54, 5306.63, 5303.44, 5299.75, 5297.73, 5296.25, 5296.14, 5295.78, 5295.71, 5295.26, 5295.32, 5293.84, 5293.24, 5292.21, 5291.3, 5290.79, 5290.79, 5290.99, 5290.91, 5290.76, 5290.4, 5290.21, 5290.27, 5290.14, 5290.21, 5290.14, 5289.92, 5290.06, 5290.24, 5290.79, 5291.01, 5291.23, 5291.47, 5291.55, 5291.54, 5291.63, 5292.22, 5292.36, 5292.76, 5292.93, 5293.1, 5293.3, 5293.71, 5294.52, 5295.17, 5295.59, 5296.64, 5297.64, 5298.69, 5299.64, 5300.45, 5301.35, 5302.08, 5302.41, 5302.61, 5303.14, 5303.81, 5304.34, 5304.57, 5304.39, 5304.19, 5303.8, 5303.38, 5303.16, 5302.8, 5302.18, 5302.1, 5301.97, 5301.94, 5301.91, 5301.86, 5301.86, 5301.86, 5301.8, 5301.8, 5301.49, 5301.1, 5300.49, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0], "middle": [5312.6, 5312.1, 5311.8, 5311.75, 5311.35, 5311.05, 5310.55, 5309.95, 5309.7, 5309.4, 5308.9, 5308.1, 5307.4, 5306.65, 5306.1, 5305.5, 5305.1, 5304.55, 5304.0, 5303.55, 5303.0, 5302.55, 5302.0, 5301.25, 5300.75, 5300.1, 5299.6, 5298.9, 5298.3, 5298.0, 5297.9, 5298.05, 5298.35, 5298.35, 5298.2, 5298.0, 5297.8, 5297.8, 5297.75, 5297.55, 5297.5, 5297.5, 5297.7, 5298.05, 5298.45, 5298.9, 5299.45, 5300.25, 5300.95, 5301.35, 5301.65, 5301.95, 5301.85, 5301.8, 5301.1, 5300.4, 5299.85, 5299.65, 5299.35, 5299.45, 5299.3, 5299.05, 5298.7, 5298.25, 5297.4, 5296.4, 5295.25, 5294.25, 5293.2, 5292.15, 5291.05, 5290.1, 5289.4, 5288.9, 5289.15, 5289.3, 5289.35, 5289.1, 5289.15, 5288.8, 5288.45, 5288.15, 5287.8, 5287.6, 5287.6, 5287.5, 5287.6, 5287.5, 5287.3, 5287.2, 5287.25, 5287.2, 5287.25, 5287.2, 5287.1, 5287.3, 5287.45, 5287.6, 5287.65, 5287.7, 5287.9, 5287.95, 5288.05, 5288.15, 5288.4, 5288.8, 5289.05, 5289.25, 5289.55, 5289.8, 5290.05, 5290.4, 5290.75, 5291.15, 5291.7, 5292.15, 5292.65, 5293.05, 5293.45, 5293.9, 5294.4, 5294.9, 5295.5, 5296.15, 5296.65, 5297.15, 5297.6, 5298.05, 5298.45, 5298.85, 5299.2, 5299.45, 5299.7, 5299.95, 5300.05, 5300.15, 5300.2, 5300.25, 5300.3, 5300.3, 5300.3, 5300.35, 5300.35, 5300.25, 5300.15, 5300.05, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0], "lower": [5305.49, 5305.17, 5304.54, 5304.4, 5303.67, 5303.66, 5303.49, 5303.9, 5303.93, 5303.94, 5303.77, 5302.1, 5299.85, 5298.9, 5298.38, 5297.84, 5297.18, 5296.01, 5294.77, 5294.27, 5293.53, 5293.22, 5292.49, 5291.39, 5290.89, 5291.09, 5291.08, 5290.63, 5291.43, 5292.45, 5292.8, 5292.67, 5292.58, 5292.58, 5292.56, 5292.34, 5292.16, 5292.16, 5292.03, 5291.74, 5291.65, 5291.65, 5291.8, 5292.48, 5292.4, 5292.06, 5291.61, 5291.96, 5291.58, 5291.19, 5290.97, 5290.91, 5290.82, 5290.71, 5287.63, 5284.67, 5282.83, 5282.41, 5281.59, 5281.77, 5281.46, 5281.06, 5280.52, 5279.78, 5278.51, 5277.32, 5276.33, 5276.25, 5276.86, 5277.67, 5278.66, 5280.45, 5281.07, 5281.55, 5282.16, 5282.82, 5282.99, 5282.94, 5282.98, 5283.76, 5283.66, 5284.09, 5284.3, 5284.41, 5284.41, 5284.01, 5284.29, 5284.24, 5284.2, 5284.19, 5284.23, 5284.26, 5284.29, 5284.26, 5284.28, 5284.54, 5284.66, 5284.41, 5284.29, 5284.17, 5284.33, 5284.35, 5284.56, 5284.67, 5284.58, 5285.24, 5285.34, 5285.57, 5286.0, 5286.3, 5286.39, 5286.28, 5286.33, 5286.71, 5286.76, 5286.66, 5286.61, 5286.46, 5286.45, 5286.45, 5286.72, 5287.39, 5288.39, 5289.16, 5289.49, 5289.96, 5290.63, 5291.71, 5292.71, 5293.9, 5295.02, 5295.74, 5296.6, 5297.72, 5298.0, 5298.33, 5298.46, 5298.59, 5298.74, 5298.74, 5298.74, 5298.9, 5298.9, 5299.01, 5299.2, 5299.61, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0], "current": {"upper": 5300.0, "middle": 5300.0, "lower": 5300.0}, "position": "within_bands"}, "stochastic": {"k_values": [46.67, 46.67, 20.0, 20.0, 13.33, 20.0, 20.0, 20.0, 15.38, 23.08, 15.38, 54.55, 30.0, 30.0, 50.0, 50.0, 12.5, 0.0, 6.25, 18.75, 31.25, 25.0, 25.0, 6.25, 5.88, 23.53, 11.76, 23.53, 6.67, 0.0, 25.0, 44.44, 44.44, 18.18, 54.55, 91.67, 80.0, 73.33, 80.0, 53.33, 46.67, 33.33, 33.33, 40.0, 26.67, 26.67, 33.33, 46.15, 53.85, 53.85, 84.62, 100.0, 100.0, 94.12, 100.0, 89.47, 80.0, 75.0, 40.0, 21.05, 12.12, 6.06, 22.22, 44.44, 33.33, 55.56, 44.44, 47.22, 44.44, 38.89, 29.41, 29.03, 33.33, 52.17, 65.0, 60.0, 23.08, 38.46, 23.08, 30.77, 41.67, 8.33, 23.08, 50.0, 70.0, 70.0, 30.0, 50.0, 33.33, 44.44, 44.44, 11.11, 44.44, 44.44, 33.33, 44.44, 55.56, 55.56, 55.56, 57.14, 66.67, 100.0, 100.0, 88.89, 88.89, 87.5, 75.0, 62.5, 33.33, 50.0, 100.0, 85.71, 85.71, 71.43, 83.33, 83.33, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 92.86, 81.82, 90.0, 100.0, 100.0, 100.0, 87.5, 71.43, 60.0, 50.0, 50.0, 50.0, 33.33, 33.33, 33.33, 33.33, 33.33, 33.33, 33.33, 33.33, 50.0, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], "d_values": [37.78, 28.89, 17.78, 17.78, 17.78, 20.0, 18.46, 19.49, 17.95, 31.0, 33.31, 38.18, 36.67, 43.33, 37.5, 20.83, 6.25, 8.33, 18.75, 25.0, 27.08, 18.75, 12.38, 11.89, 13.72, 19.61, 13.99, 10.07, 10.56, 23.15, 37.96, 35.69, 39.06, 54.8, 75.41, 81.67, 77.78, 68.89, 60.0, 44.44, 37.78, 35.55, 33.33, 31.11, 28.89, 35.38, 44.44, 51.28, 64.11, 79.49, 94.87, 98.04, 98.04, 94.53, 89.82, 81.49, 65.0, 45.35, 24.39, 13.08, 13.47, 24.24, 33.33, 44.44, 44.44, 49.07, 45.37, 43.52, 37.58, 32.44, 30.59, 38.18, 50.17, 59.06, 49.36, 40.51, 28.21, 30.77, 31.84, 26.92, 24.36, 27.14, 47.69, 63.33, 56.67, 50.0, 37.78, 42.59, 40.74, 33.33, 33.33, 33.33, 40.74, 40.74, 44.44, 51.85, 55.56, 56.09, 59.79, 74.6, 88.89, 96.3, 92.59, 88.43, 83.8, 75.0, 56.94, 48.61, 61.11, 78.57, 90.47, 80.95, 80.16, 79.36, 88.89, 94.44, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 97.62, 91.56, 88.23, 90.61, 96.67, 100.0, 95.83, 86.31, 72.98, 60.48, 53.33, 50.0, 44.44, 38.89, 33.33, 33.33, 33.33, 33.33, 33.33, 33.33, 38.89, 61.11, 83.33, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0, 100.0], "current": {"k": 100, "d": 100.0}, "signal": "overbought"}, "atr": {"values": [4.142857142857143, 4.2, 4.04, 3.97, 3.9, 3.84, 3.78, 3.87, 3.81, 3.9, 3.91, 3.84, 3.71, 3.8, 3.74, 3.69, 3.85, 3.93, 3.86, 3.8, 3.67, 3.48, 3.52, 3.41, 3.45, 3.42, 3.53, 3.49, 3.46, 3.5, 3.32, 3.3, 3.49, 3.81, 3.97, 3.97, 3.9, 3.84, 4.07, 3.92, 3.78, 3.8, 3.67, 3.55, 3.51, 3.4, 3.51, 3.47, 3.36, 3.55, 3.51, 3.4, 3.3, 3.21, 3.19, 3.32, 3.3, 3.71, 3.73, 4.89, 4.97, 5.19, 5.53, 5.56, 5.73, 5.61, 5.5, 5.39, 5.36, 5.33, 5.16, 4.93, 5.08, 5.0, 4.86, 4.8, 4.74, 4.62, 4.5, 4.32, 4.44, 4.41, 4.31, 4.22, 4.13, 4.26, 4.17, 4.16, 4.01, 3.79, 3.81, 3.82, 3.62, 3.58, 3.4, 3.3, 3.14, 2.99, 2.92, 2.78, 2.72, 2.6, 2.63, 2.58, 2.54, 2.5, 2.39, 2.36, 2.33, 2.38, 2.35, 2.25, 2.45, 2.49, 2.46, 2.43, 2.4, 2.3, 2.21, 2.27, 2.18, 2.1, 2.02, 1.95, 1.88, 1.82, 1.83, 1.77, 1.79, 1.73, 1.68, 1.7, 1.72, 1.6, 1.49, 1.38, 1.28, 1.19, 1.1, 1.02, 0.95, 0.88, 0.82, 0.76, 0.71, 0.66, 0.61, 0.57, 0.53, 0.49, 0.46, 0.43, 0.4, 0.37, 0.34, 0.32, 0.3, 0.28, 0.26, 0.24, 0.22, 0.2, 0.19, 0.18, 0.17, 0.16, 0.15, 0.14, 0.13, 0.12, 0.11, 0.1, 0.09, 0.08, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07], "current": 0.07, "volatility": "high"}, "adx": {"adx": [41.105959421525824, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, 41.11, NaN, NaN, NaN, NaN, NaN, NaN], "plus_di": [13.79, 304.65, 6990.79, 163434.83, 3888846.19, 94087218.1, 2312422060.08, 55550182756.91, 1356142277145.91, 32379907080701.48, 771546231363955.2, 1.8691661581316104e+16, 4.68843095739183e+17, 1.14726674568692e+19, 2.8500137936712532e+20, 7.181204125706552e+21, 1.731757676671204e+23, 4.088973468310023e+24, 9.821129953712705e+25, 2.3972537672788347e+27, 6.056667188149111e+28, 1.6141440733385276e+30, 4.256797035411169e+31, 1.1583385208605613e+33, 3.11371736790648e+34, 8.449338096083285e+35, 2.2196834846979214e+37, 5.8949104150926134e+38, 1.581577759575656e+40, 4.196618793818342e+41, 1.1734113734751682e+43, 3.3037684577491834e+44, 8.787601851062772e+45, 2.1399654072481805e+47, 5.0061827974205134e+48, 1.1704878128618005e+50, 2.7853674516420572e+51, 6.739529595363781e+52, 1.540063719902044e+54, 3.6516883924702555e+55, 8.972196400641801e+56, 2.1953106243933784e+58, 5.559294522270479e+59, 1.4550524603556857e+61, 3.8508258590395103e+62, 1.0514210394413907e+64, 2.7774921605740446e+65, 7.414790178694356e+66, 2.0414248172615058e+68, 5.324165680325022e+69, 1.404364807073563e+71, 3.822227640441341e+72, 1.0719708007045992e+74, 3.093918113664944e+75, 8.972940728688815e+76, 2.5019495931303067e+78, 7.026016603184824e+79, 1.7569737758861094e+81, 4.369510650858875e+82, 8.287779031332634e+83, 1.547040238247487e+85, 2.76755715992913e+86, 4.64365839125378e+87, 7.744984173458252e+88, 1.2526624450041007e+90, 2.0708995908341543e+91, 3.495478231890031e+92, 6.017325446454934e+93, 1.0412924834585662e+95, 1.810767492261079e+96, 3.2505942195642086e+97, 6.102658242333648e+98, 1.1127064946300173e+100, 2.0603926179853446e+101, 3.927938575454016e+102, 7.585095521094527e+103, 1.4825366055187418e+105, 2.9760159317621627e+106, 6.127833615351252e+107, 1.3139976857289266e+109, 2.742102401726693e+110, 5.763932643167215e+111, 1.2400073717911191e+113, 2.727057474403582e+114, 6.1240459694662135e+115, 1.3323289208111404e+117, 2.9614251763835933e+118, 6.602536360219214e+119, 1.528806243516811e+121, 3.740479144338919e+122, 9.116574804941643e+123, 2.214067591050507e+125, 5.676544056550578e+126, 1.473451040830714e+128, 4.032111849039134e+129, 1.1367294823179997e+131, 3.3724099664731867e+132, 1.0516319889061715e+134, 3.358107213666263e+135, 1.1250479046659457e+137, 3.8456613425680187e+138, 1.3766669556911284e+140, 4.873684359396954e+141, 1.7551588507132667e+143, 6.42378427560263e+144, 2.387167605152409e+146, 9.26811993044888e+147, 3.640917024583344e+149, 1.4462046359606097e+151, 5.630519803000923e+152, 2.217708237026722e+154, 9.109633412159924e+155, 3.443848565132141e+157, 1.2816623719350242e+159, 4.838409275009696e+160, 1.8512644786914476e+162, 7.17340689066969e+163, 2.9003177262011956e+165, 1.2219194916321373e+167, 5.018555288747895e+168, 2.1466891364936023e+170, 9.550397962951732e+171, 4.4130671418303475e+173, 2.1150966684060844e+175, 1.0500137828539682e+177, 5.391896894369373e+178, 2.74796902714692e+180, 1.447130302017629e+182, 7.548463728652394e+183, 4.064640294295921e+185, 2.256406467962673e+187, 1.23533519686315e+189, 6.677732900911024e+190, 3.887388592230949e+192, 2.437089907348176e+194, 1.6453936988634654e+196, 1.1963349537165019e+198, 9.367429586381963e+199, 7.899011862246878e+201, 7.173147779627425e+203, 7.015061353142744e+205, 7.388186547116623e+207, 8.379708567257023e+209, 1.0235396249871907e+212, 1.3463720566217052e+214, 1.9072613227803043e+216, 2.9096449515663467e+218, 4.78029255887255e+220, 8.457726540075496e+222, 1.6115266246220697e+225, 3.306785421303488e+227, 7.307338518770893e+229, 1.7389901531065065e+232, 4.456764664902359e+234, 1.2300618945194487e+237, 3.656107114876371e+239, 1.170295514459614e+242, 4.0341946888897343e+244, 1.4976242010603774e+247, 5.987334597963716e+249, 2.577798057012205e+252, 1.1952229904405487e+255, 5.968066696984325e+257, 3.209246535734143e+260, 1.8584769432770182e+263, 1.159033416129832e+266, 7.784297411222433e+268, 5.630248544925815e+271, 4.385512941507289e+274, 3.678729967975407e+277, 3.323227759021132e+280, 3.233009545781084e+283, 3.3871821367814126e+286, 3.8216842022352505e+289, 4.643609887227995e+292, 6.076329842906085e+295, 8.562717925034927e+298, 1.2994710777850244e+302, 2.1237641307321954e+305, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity], "minus_di": [32.76, 730.35, 16760.84, 391846.63, 9323785.58, 225580798.95, 5544196399.45, 133185515103.84, 3251447587961.32, 77633130793279.1, 1849836978940513.5, 4.481458840105427e+16, 1.1240846764115314e+18, 2.7506536414916293e+19, 6.833110825651453e+20, 1.7217447775706624e+22, 4.152012230307112e+23, 9.803604787512018e+24, 2.354685775762424e+26, 5.747586452178495e+27, 1.452129046624658e+29, 3.8700252490647695e+30, 1.0205973728920218e+32, 2.777199010137277e+33, 7.465358905247082e+34, 2.0257889187113048e+36, 5.321849067007935e+37, 1.4133467095163525e+39, 3.791945194989667e+40, 1.0061692113508484e+42, 2.813337246591792e+43, 7.921019913735812e+44, 2.106890060439409e+46, 5.130719305028693e+47, 1.2002679406045718e+49, 2.80632780203366e+50, 6.678116621574482e+51, 1.6158501667655668e+53, 3.692412331485569e+54, 8.755182708906039e+55, 2.1511479169412165e+57, 5.263413400496818e+58, 1.3328804115778619e+60, 3.4885917888627874e+61, 9.232628952018549e+62, 2.5208567421247342e+64, 6.659235050975951e+65, 1.7777486955494635e+67, 4.894461230173322e+68, 1.2765065989707743e+70, 3.367064534104214e+71, 9.164062688398555e+72, 2.5701262567018217e+74, 7.417888784646938e+75, 2.1513263748857777e+77, 5.998602142914741e+78, 1.6845374650129478e+80, 4.212469622095423e+81, 1.0476212640614023e+83, 1.9870539835758602e+84, 3.709139030541364e+85, 6.635415180135193e+86, 1.113350135159504e+88, 1.856915054856092e+89, 3.0033473286522196e+90, 4.965129096704397e+91, 8.380657735831253e+92, 1.4426965841689527e+94, 2.496572808591449e+95, 4.341443884090247e+96, 7.793530894772335e+97, 1.4631557290543825e+99, 2.667792980901648e+100, 4.939937881813627e+101, 9.417512175565544e+102, 1.8185806129740394e+104, 3.55448698216483e+105, 7.135209915752747e+106, 1.4691917038376778e+108, 3.15040293179401e+109, 6.57438558644563e+110, 1.3819438568968131e+112, 2.9730058903184628e+113, 6.538314302864134e+114, 1.4682835887907168e+116, 3.19435337202205e+117, 7.100227541718935e+118, 1.5830050640440984e+120, 3.6654217309739975e+121, 8.968064853251715e+122, 2.1857636665071403e+124, 5.308384562463057e+125, 1.3609918215566847e+127, 3.532703694461178e+128, 9.667275010067859e+129, 2.7253898029239706e+131, 8.085592814187233e+132, 2.5213625084738853e+134, 8.051300946807981e+135, 2.6973823894538353e+137, 9.220246656364763e+138, 3.3006569649377487e+140, 1.1685004974696941e+142, 4.208118209876354e+143, 1.5401479800814297e+145, 5.723404160931909e+146, 2.2220976884679365e+148, 8.729357588101864e+149, 3.467378500424778e+151, 1.3499571793429867e+153, 5.317113270193414e+154, 2.184099418205354e+156, 8.256871936747927e+157, 3.072876716578283e+159, 1.1600430450342854e+161, 4.4385382900886e+162, 1.7198753349995836e+164, 6.953718082608064e+165, 2.9296389108315973e+167, 1.2032343334205348e+169, 5.146839924233297e+170, 2.28977585493934e+172, 1.0580642426408555e+174, 5.071094734446062e+175, 2.5174827443415173e+177, 1.2927456393904625e+179, 6.588451238253357e+180, 3.4695978506501e+182, 1.8097978801305995e+184, 9.74526427170559e+185, 5.409895031927059e+187, 2.9618040185413727e+189, 1.601033969637248e+191, 9.32029070598641e+192, 5.843096432012431e+194, 3.944948449417757e+196, 2.8682981610468655e+198, 2.2459078849854033e+200, 1.893844289025021e+202, 1.7198132112838352e+204, 1.6819108658776557e+206, 1.7713702856154783e+208, 2.0090947438176857e+210, 2.4540090674346914e+212, 3.228022789182887e+214, 4.572794707512105e+216, 6.976080768977308e+218, 1.1461091488871891e+221, 2.0278001078354e+223, 3.863749729556852e+225, 7.9282532984897e+227, 1.7519863956515586e+230, 4.1693526070926824e+232, 1.0685410346697419e+235, 2.9491609010197327e+237, 8.765776910230894e+239, 2.805866752933948e+242, 9.672268766786801e+244, 3.590660565835733e+247, 1.435506064882664e+250, 6.180454231073513e+252, 2.8656321499856155e+255, 1.4308864485473952e+258, 7.694396881238005e+260, 4.455830687040755e+263, 2.778865070981505e+266, 1.866340683291798e+269, 1.349892143289698e+272, 1.0514579270876549e+275, 8.820016809968015e+277, 7.967674972906486e+280, 7.751370388370635e+283, 8.121010143421096e+286, 9.162759756638234e+289, 1.1133385059742555e+293, 1.4568433079864557e+296, 2.0529725393078214e+299, 3.1155743557983795e+302, 5.091875591992431e+305, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity], "current": {"adx": NaN, "plus_di": Infinity, "minus_di": Infinity}, "trend_strength": "very_strong"}, "ichimoku": {"tenkan": [5312.5, 5312.5, 5312.5, 5312.5, 5315.5, 5315.5, 5315.5, 5313.5, 5312.0, 5311.5, 5311.0, 5310.0, 5309.5, 5309.5, 5309.5, 5308.0, 5307.5, 5307.5, 5307.5, 5308.0, 5308.0, 5308.0, 5305.5, 5304.0, 5304.0, 5304.0, 5304.0, 5304.0, 5303.0, 5301.5, 5300.0, 5298.5, 5298.5, 5298.5, 5297.5, 5297.0, 5297.0, 5297.0, 5296.0, 5296.0, 5297.0, 5298.5, 5298.5, 5298.5, 5298.5, 5298.5, 5298.5, 5299.5, 5300.5, 5300.5, 5299.0, 5298.5, 5298.5, 5297.0, 5297.0, 5299.5, 5299.5, 5300.5, 5301.5, 5302.5, 5303.5, 5305.5, 5306.0, 5306.0, 5305.0, 5296.5, 5296.5, 5295.0, 5295.0, 5295.0, 5294.0, 5292.5, 5289.0, 5288.5, 5287.0, 5287.0, 5290.5, 5291.0, 5290.5, 5290.5, 5290.0, 5290.0, 5290.0, 5288.5, 5288.5, 5288.5, 5288.5, 5288.0, 5287.0, 5287.0, 5287.5, 5287.5, 5287.5, 5287.5, 5287.5, 5287.5, 5287.5, 5287.5, 5287.5, 5286.5, 5285.5, 5286.0, 5286.0, 5286.0, 5286.0, 5286.5, 5286.5, 5286.5, 5289.0, 5289.0, 5289.0, 5289.0, 5289.0, 5289.0, 5289.5, 5290.0, 5290.5, 5290.5, 5290.0, 5290.0, 5290.0, 5290.0, 5291.0, 5291.0, 5291.0, 5292.5, 5292.5, 5294.5, 5295.0, 5295.0, 5296.5, 5297.5, 5297.5, 5298.0, 5299.5, 5300.0, 5300.0, 5300.0, 5300.5, 5300.5, 5300.5, 5300.5, 5300.5, 5300.5, 5300.5, 5300.5, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0], "kijun": [5312.0, 5312.0, 5312.0, 5312.0, 5312.0, 5309.5, 5308.0, 5308.0, 5307.0, 5307.0, 5306.5, 5305.5, 5304.5, 5304.5, 5304.5, 5303.5, 5303.5, 5302.5, 5302.5, 5302.5, 5302.5, 5301.5, 5301.5, 5301.5, 5301.5, 5301.5, 5301.5, 5301.5, 5300.5, 5299.5, 5298.5, 5298.5, 5298.5, 5298.5, 5298.5, 5298.5, 5298.5, 5298.5, 5298.5, 5298.5, 5299.5, 5300.5, 5301.0, 5301.5, 5302.0, 5302.0, 5302.0, 5303.0, 5296.5, 5296.5, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5294.0, 5292.5, 5289.0, 5288.5, 5287.0, 5287.0, 5290.0, 5290.0, 5290.0, 5290.0, 5289.5, 5289.5, 5289.5, 5288.0, 5288.0, 5288.0, 5288.0, 5288.0, 5287.5, 5287.5, 5287.5, 5287.5, 5287.5, 5287.5, 5287.5, 5287.5, 5287.5, 5287.5, 5287.5, 5288.0, 5288.0, 5288.0, 5288.0, 5288.0, 5288.0, 5289.5, 5289.5, 5289.5, 5292.0, 5292.0, 5292.5, 5292.5, 5292.5, 5293.0, 5294.0, 5294.0, 5294.0, 5294.5, 5294.5, 5294.5, 5294.5, 5294.5, 5294.5, 5294.5, 5294.5, 5294.5, 5294.5, 5296.0, 5296.5, 5296.5, 5297.5, 5298.0, 5298.0, 5298.5, 5299.5, 5300.0, 5300.0, 5300.0, 5300.5, 5300.5, 5300.5, 5300.5, 5300.5, 5300.5, 5300.5, 5300.5, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0], "senkou_a": [5312.25, 5312.25, 5312.25, 5312.25, 5313.75, 5312.5, 5311.75, 5310.75, 5309.5, 5309.25, 5308.75, 5307.75, 5307.0, 5307.0, 5307.0, 5305.75, 5305.5, 5305.0, 5305.0, 5305.25, 5305.25, 5304.75, 5303.5, 5302.75, 5302.75, 5302.75, 5302.75, 5302.75, 5301.75, 5300.5, 5299.25, 5298.5, 5298.5, 5298.5, 5298.0, 5297.75, 5297.75, 5297.75, 5297.25, 5297.25, 5298.25, 5299.5, 5299.75, 5300.0, 5300.25, 5300.25, 5300.25, 5301.25, 5298.5, 5298.5, 5297.0, 5296.75, 5296.75, 5296.0, 5296.0, 5297.25, 5297.25, 5297.75, 5298.25, 5298.75, 5299.25, 5300.25, 5300.5, 5300.5, 5300.0, 5295.75, 5295.75, 5295.0, 5295.0, 5295.0, 5294.0, 5292.5, 5289.0, 5288.5, 5287.0, 5287.0, 5290.25, 5290.5, 5290.25, 5290.25, 5289.75, 5289.75, 5289.75, 5288.25, 5288.25, 5288.25, 5288.25, 5288.0, 5287.25, 5287.25, 5287.5, 5287.5, 5287.5, 5287.5, 5287.5, 5287.5, 5287.5, 5287.5, 5287.5, 5287.25, 5286.75, 5287.0, 5287.0, 5287.0, 5287.0, 5288.0, 5288.0, 5288.0, 5290.5, 5290.5, 5290.75, 5290.75, 5290.75, 5291.0, 5291.75, 5292.0, 5292.25, 5292.5, 5292.25, 5292.25, 5292.25, 5292.25, 5292.75, 5292.75, 5292.75, 5293.5, 5293.5, 5295.25, 5295.75, 5295.75, 5297.0, 5297.75, 5297.75, 5298.25, 5299.5, 5300.0, 5300.0, 5300.0, 5300.5, 5300.5, 5300.5, 5300.5, 5300.5, 5300.5, 5300.5, 5300.5, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0], "senkou_b": [5305.5, 5305.5, 5305.5, 5305.5, 5305.5, 5305.5, 5305.5, 5305.5, 5304.5, 5304.5, 5304.0, 5303.0, 5302.5, 5302.5, 5302.5, 5301.5, 5301.5, 5301.5, 5302.0, 5302.0, 5302.0, 5302.0, 5296.5, 5296.5, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5295.0, 5294.0, 5292.5, 5289.0, 5288.5, 5287.0, 5287.0, 5290.0, 5290.0, 5290.0, 5290.0, 5289.5, 5289.5, 5290.5, 5290.5, 5291.0, 5291.0, 5291.0, 5291.5, 5292.0, 5292.0, 5292.0, 5292.5, 5292.5, 5292.5, 5292.5, 5292.5, 5292.5, 5292.5, 5292.5, 5292.5, 5292.5, 5292.5, 5292.5, 5292.5, 5292.5, 5293.0, 5293.0, 5293.0, 5294.0, 5294.0, 5294.0, 5294.0, 5294.0, 5294.0, 5294.5, 5294.5, 5294.5, 5294.5, 5294.5, 5294.5, 5294.5, 5294.5, 5294.5, 5294.5, 5294.5, 5294.5, 5294.5, 5296.0, 5296.5, 5296.5, 5297.5, 5298.0, 5298.0, 5298.5, 5299.5, 5300.0, 5300.0, 5300.0, 5300.5, 5300.5, 5300.5, 5300.5, 5300.5, 5300.5, 5300.5, 5300.5, 5300.0, 5300.0], "chikou": [5306.0, 5307.0, 5307.0, 5305.0, 5299.0, 5296.0, 5297.0, 5299.0, 5300.0, 5299.0, 5296.0, 5295.0, 5296.0, 5296.0, 5296.0, 5296.0, 5293.0, 5293.0, 5296.0, 5295.0, 5291.0, 5293.0, 5297.0, 5302.0, 5302.0, 5300.0, 5297.0, 5298.0, 5296.0, 5295.0, 5295.0, 5295.0, 5293.0, 5294.0, 5295.0, 5298.0, 5299.0, 5300.0, 5303.0, 5306.0, 5308.0, 5309.0, 5309.0, 5308.0, 5308.0, 5299.0, 5297.0, 5280.0, 5280.0, 5277.0, 5284.0, 5287.0, 5289.0, 5293.0, 5291.0, 5291.0, 5291.0, 5286.0, 5285.0, 5285.0, 5284.0, 5289.0, 5288.0, 5287.0, 5285.0, 5286.0, 5287.0, 5287.0, 5284.0, 5283.0, 5286.0, 5287.0, 5289.0, 5284.0, 5285.0, 5284.0, 5286.0, 5287.0, 5283.0, 5284.0, 5287.0, 5284.0, 5286.0, 5287.0, 5287.0, 5288.0, 5286.0, 5286.0, 5287.0, 5288.0, 5289.0, 5290.0, 5290.0, 5289.0, 5289.0, 5288.0, 5288.0, 5289.0, 5291.0, 5291.0, 5287.0, 5290.0, 5291.0, 5291.0, 5293.0, 5294.0, 5294.0, 5295.0, 5297.0, 5298.0, 5298.0, 5298.0, 5299.0, 5300.0, 5299.0, 5299.0, 5300.0, 5301.0, 5301.0, 5300.0, 5299.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0, 5300.0], "current": {"tenkan": 5300.0, "kijun": 5300.0, "senkou_a": 5300.0, "senkou_b": 5300.0, "chikou": 5300.0}, "cloud_signal": "in_cloud"}, "overall_signal": {"signal": "neutral", "confidence": 66.67, "breakdown": {"bullish": 0, "bearish": 1, "neutral": 2}, "recommendation": "hold"}}}