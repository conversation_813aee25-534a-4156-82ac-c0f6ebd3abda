{"asset": "TRXUSD_otc", "timeframe": 300, "timestamp": 1753230103.702025, "last_update": "2025-07-23T03:21:43.702024", "indicators": {"asset": "TRXUSD_otc", "timeframe": 300, "last_update": "2025-07-23T03:21:43.677007", "candles_count": 199, "timestamps": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "rsi": {"values": [44.57, 47.43, 39.72, 38.54, 35.41, 35.74, 35.4, 40.17, 34.15, 29.99, 27.77, 27.63, 25.93, 38.13, 46.0, 45.43, 46.72, 52.54, 55.99, 50.89, 57.14, 50.0, 52.76, 59.11, 52.37, 59.05, 63.4, 64.23, 65.83, 68.4, 68.46, 67.39, 69.95, 68.17, 69.12, 71.31, 73.13, 73.5, 74.34, 66.3, 67.49, 69.11, 61.99, 57.23, 51.84, 52.02, 47.19, 58.84, 57.67, 58.05, 54.18, 55.26, 52.83, 54.97, 47.17, 49.39, 48.76, 54.73, 53.27, 57.72, 57.78, 49.9, 50.3, 59.44, 52.07, 47.01, 44.6, 49.9, 48.78, 50.85, 49.14, 53.86, 57.86, 55.05, 51.26, 57.41, 63.51, 53.73, 57.03, 53.82, 54.84, 54.84, 52.05, 49.97, 46.11, 41.26, 46.55, 42.35, 37.66, 37.92, 32.91, 34.2, 37.02, 37.17, 36.34, 39.34, 40.4, 41.09, 38.03, 43.12, 48.03, 44.99, 47.38, 39.35, 39.18, 43.96, 42.2, 52.95, 57.05, 60.26, 64.92, 66.28, 65.01, 59.92, 57.8, 53.36, 47.25, 46.45, 51.03, 55.78, 57.45, 58.62, 60.26, 54.45, 50.07, 53.48, 51.44, 52.67, 50.8, 51.52, 45.57, 46.02, 58.82, 55.77, 45.05, 37.73, 37.07, 49.31, 46.01, 48.91, 49.19, 50.89, 54.91, 57.37, 51.17, 54.65, 55.61, 65.71, 64.49, 63.57, 65.29, 60.66, 62.22, 60.77, 62.36, 70.54, 66.86, 63.65, 61.16, 57.18, 58.02, 56.89, 59.75, 52.79, 56.77, 59.84, 58.91, 50.64, 52.83, 53.87, 50.42, 49.64, 40.3, 33.65, 36.13, 35.57, 37.89, 35.87, 31.9, 28.84, 26.2, 23.22, 22.57, 26.6, 32.45], "current": 32.45, "signal": "neutral"}, "macd": {"macd_line": [-0.01, -0.01, -0.01, -0.01, -0.01, -0.01, -0.01, -0.01, -0.01, -0.01, -0.01, -0.01, -0.01, -0.01, -0.01, 0.0, 0.01, 0.01, 0.02, 0.02, 0.02, 0.03, 0.03, 0.03, 0.04, 0.03, 0.03, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.03, 0.02, 0.01, 0.01, 0.01, 0.01], "signal_line": [-0.01, -0.01, -0.01, -0.01, -0.01, -0.01, -0.01, -0.01, -0.01, -0.01, -0.0, 0.0, 0.0, 0.01, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02], "histogram": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.0, -0.01, -0.01, -0.01, -0.01], "current": {"macd": 0.01, "signal": 0.02, "histogram": -0.01}, "trend": "bearish"}, "moving_averages": {"sma_20": {"values": [0.14, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.13, 0.13, 0.13, 0.14, 0.14, 0.15, 0.15, 0.15, 0.16, 0.16, 0.16, 0.17, 0.17, 0.17, 0.18, 0.18, 0.18, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.18, 0.18, 0.19, 0.19, 0.19, 0.19, 0.19, 0.18, 0.18, 0.18, 0.18, 0.18, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.18, 0.18, 0.18, 0.18, 0.18, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.19, 0.19, 0.19, 0.19, 0.19, 0.2, 0.2, 0.2, 0.2, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.22, 0.22, 0.22, 0.22, 0.22, 0.22, 0.21, 0.21, 0.21, 0.21, 0.21, 0.2, 0.2, 0.2, 0.2, 0.19, 0.19, 0.19, 0.18], "current": 0.18}, "ema_20": {"values": [0.13561499999999999, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.14, 0.14, 0.14, 0.15, 0.15, 0.15, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.17, 0.17, 0.17, 0.17, 0.17, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18], "current": 0.18}, "sma_50": {"values": [0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.19, 0.19, 0.19, 0.19], "current": 0.19}, "ema_50": {"values": [0.138736, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14], "current": 0.14}, "trend": "bearish"}, "bollinger_bands": {"upper": [0.16, 0.15, 0.15, 0.15, 0.15, 0.16, 0.16, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.16, 0.17, 0.18, 0.18, 0.19, 0.19, 0.2, 0.2, 0.2, 0.21, 0.21, 0.21, 0.22, 0.22, 0.22, 0.22, 0.22, 0.22, 0.22, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.2, 0.19, 0.19, 0.2, 0.2, 0.2, 0.2, 0.2, 0.19, 0.19, 0.19, 0.19, 0.19, 0.2, 0.2, 0.2, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.22, 0.22, 0.22, 0.22, 0.22, 0.22, 0.22, 0.21, 0.2, 0.2, 0.2, 0.2, 0.19, 0.18, 0.18, 0.18, 0.17, 0.17, 0.17, 0.18, 0.18, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.21, 0.21, 0.21, 0.21, 0.2, 0.2, 0.2, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.21, 0.21, 0.21, 0.22, 0.22, 0.23, 0.23, 0.23, 0.24, 0.24, 0.23, 0.23, 0.24, 0.24, 0.24, 0.23, 0.23, 0.23, 0.23, 0.23, 0.23, 0.23, 0.23, 0.23, 0.23, 0.23, 0.24, 0.24, 0.24, 0.23, 0.23, 0.24, 0.24, 0.24, 0.24, 0.24, 0.23], "middle": [0.14, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.13, 0.13, 0.13, 0.14, 0.14, 0.15, 0.15, 0.15, 0.16, 0.16, 0.16, 0.17, 0.17, 0.17, 0.18, 0.18, 0.18, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.18, 0.18, 0.19, 0.19, 0.19, 0.19, 0.19, 0.18, 0.18, 0.18, 0.18, 0.18, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.18, 0.18, 0.18, 0.18, 0.18, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.19, 0.19, 0.19, 0.19, 0.19, 0.2, 0.2, 0.2, 0.2, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.22, 0.22, 0.22, 0.22, 0.22, 0.22, 0.21, 0.21, 0.21, 0.21, 0.21, 0.2, 0.2, 0.2, 0.2, 0.19, 0.19, 0.19, 0.18], "lower": [0.12, 0.11, 0.11, 0.11, 0.11, 0.1, 0.1, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.08, 0.09, 0.08, 0.08, 0.09, 0.09, 0.1, 0.1, 0.1, 0.11, 0.11, 0.11, 0.12, 0.12, 0.12, 0.14, 0.14, 0.14, 0.16, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.18, 0.17, 0.17, 0.18, 0.18, 0.18, 0.18, 0.18, 0.17, 0.17, 0.17, 0.17, 0.17, 0.18, 0.18, 0.18, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.16, 0.16, 0.14, 0.14, 0.14, 0.14, 0.14, 0.13, 0.14, 0.14, 0.14, 0.14, 0.15, 0.14, 0.14, 0.14, 0.15, 0.15, 0.15, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.15, 0.15, 0.15, 0.15, 0.16, 0.16, 0.16, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.15, 0.15, 0.15, 0.16, 0.16, 0.15, 0.15, 0.15, 0.16, 0.16, 0.17, 0.17, 0.18, 0.18, 0.18, 0.19, 0.19, 0.19, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.19, 0.19, 0.18, 0.18, 0.18, 0.17, 0.17, 0.16, 0.16, 0.14, 0.14, 0.14, 0.13], "current": {"upper": 0.23, "middle": 0.18, "lower": 0.13}, "position": "within_bands"}, "stochastic": {"k_values": [62.03, 59.42, 71.3, 29.86, 22.61, 2.61, 14.65, 12.85, 26.99, 7.94, 3.77, 3.36, 2.71, 2.48, 26.57, 46.37, 48.91, 63.04, 92.79, 96.77, 79.14, 98.39, 72.31, 83.27, 98.25, 75.87, 98.37, 100.0, 89.18, 94.98, 96.61, 87.04, 84.31, 91.62, 87.7, 87.33, 93.72, 98.58, 91.64, 92.51, 75.9, 73.22, 81.68, 57.78, 39.23, 17.95, 11.2, 1.0, 56.39, 51.63, 53.63, 38.85, 43.86, 39.04, 49.16, 14.89, 26.75, 26.0, 58.0, 51.0, 76.67, 76.69, 36.3, 49.83, 88.0, 55.71, 29.71, 16.29, 42.0, 36.29, 46.29, 38.29, 60.86, 82.29, 70.57, 54.0, 86.29, 96.9, 58.21, 74.41, 61.83, 66.52, 62.44, 51.44, 35.85, 10.53, 1.23, 23.92, 10.62, 4.1, 4.85, 5.46, 8.72, 14.9, 16.93, 13.76, 20.28, 22.57, 26.93, 20.79, 37.17, 53.4, 52.3, 79.08, 24.58, 21.3, 46.09, 33.91, 98.27, 100.0, 85.23, 98.48, 91.17, 85.45, 73.56, 68.53, 57.54, 40.41, 37.99, 47.65, 62.08, 66.6, 60.61, 67.99, 48.16, 31.44, 45.61, 40.24, 51.89, 43.99, 47.08, 21.65, 23.37, 82.33, 69.26, 13.43, 2.72, 13.08, 59.81, 43.83, 56.42, 57.63, 64.65, 82.08, 87.53, 65.53, 80.05, 84.13, 96.33, 93.46, 91.39, 97.91, 79.96, 86.71, 82.94, 87.23, 99.64, 85.17, 77.66, 71.28, 61.07, 57.02, 36.18, 51.03, 23.3, 43.66, 60.77, 56.97, 22.26, 32.94, 37.98, 30.62, 26.36, 3.37, 3.13, 15.69, 14.57, 19.78, 12.23, 4.14, 5.04, 4.08, 3.52, 3.9, 9.65, 18.55], "d_values": [64.25, 53.53, 41.26, 18.36, 13.29, 10.04, 18.16, 15.93, 12.9, 5.02, 3.28, 2.85, 10.59, 25.14, 40.62, 52.77, 68.25, 84.2, 89.57, 91.43, 83.28, 84.66, 84.61, 85.8, 90.83, 91.41, 95.85, 94.72, 93.59, 92.88, 89.32, 87.66, 87.88, 88.88, 89.58, 93.21, 94.65, 94.24, 86.68, 80.54, 76.93, 70.89, 59.56, 38.32, 22.79, 10.05, 22.86, 36.34, 53.88, 48.04, 45.45, 40.58, 44.02, 34.36, 30.27, 22.55, 36.92, 45.0, 61.89, 68.12, 63.22, 54.27, 58.04, 64.51, 57.81, 33.9, 29.33, 31.53, 41.53, 40.29, 48.48, 60.48, 71.24, 68.95, 70.29, 79.06, 80.47, 76.51, 64.82, 67.59, 63.6, 60.13, 49.91, 32.61, 15.87, 11.89, 11.92, 12.88, 6.52, 4.8, 6.34, 9.69, 13.52, 15.2, 16.99, 18.87, 23.26, 23.43, 28.3, 37.12, 47.62, 61.59, 51.99, 41.65, 30.66, 33.77, 59.42, 77.39, 94.5, 94.57, 91.63, 91.7, 83.39, 75.85, 66.54, 55.49, 45.31, 42.02, 49.24, 58.78, 63.1, 65.07, 58.92, 49.2, 41.74, 39.1, 45.91, 45.37, 47.65, 37.57, 30.7, 42.45, 58.32, 55.01, 28.47, 9.74, 25.2, 38.91, 53.35, 52.63, 59.57, 68.12, 78.09, 78.38, 77.7, 76.57, 86.84, 91.31, 93.73, 94.25, 89.75, 88.19, 83.2, 85.63, 89.94, 90.68, 87.49, 78.04, 70.0, 63.12, 51.42, 48.08, 36.84, 39.33, 42.58, 53.8, 46.67, 37.39, 31.06, 33.85, 31.65, 20.12, 10.95, 7.4, 11.13, 16.68, 15.53, 12.05, 7.14, 4.42, 4.21, 3.83, 5.69, 10.7], "current": {"k": 18.55, "d": 10.7}, "signal": "oversold"}, "atr": {"values": [0.011371428571428572, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01], "current": 0.01, "volatility": "high"}, "adx": {"adx": [10.34337705692866, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, 10.34, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN], "plus_di": [13.88, 112073.6, 877016363.3, 6979179913054.18, 5.607785127661398e+16, 4.598423998878691e+20, 3.861444782842757e+24, 3.31666233253619e+28, 2.7385648023937085e+32, 2.182839459377266e+36, 1.722456846702685e+40, 1.3944491133582096e+44, 1.1308048462824975e+48, 8.933274051032692e+51, 6.949703804192662e+55, 5.499913483437608e+59, 4.358761027230239e+63, 3.435881137467499e+67, 2.7279186612835603e+71, 2.143518985445315e+75, 1.6525246441451434e+79, 1.252784023485977e+83, 9.68079215849483e+86, 7.295016151274844e+90, 5.419374227165767e+94, 3.8541794410440236e+98, 2.671707436995165e+102, 1.8820829204170937e+106, 1.3461219130846274e+110, 9.595285380567149e+113, 6.935708597468553e+117, 5.01738597711938e+121, 3.604292761773116e+125, 2.648098372620524e+129, 1.938331182854618e+133, 1.395898364898936e+137, 1.0227934706708025e+141, 7.613135521943906e+144, 5.711784354818937e+148, 4.337272148983522e+152, 3.3648449869804068e+156, 2.633529194093496e+160, 2.0820192265248207e+164, 1.6646880867794556e+168, 1.3054257739047485e+172, 1.0358264297982392e+176, 8.139688697231855e+179, 5.987893264337063e+183, 4.443226405616481e+187, 3.412212248190786e+191, 2.6296238442340446e+195, 2.0738430861728742e+199, 1.668673919630491e+203, 1.3363798810771662e+207, 1.0326885718569546e+211, 8.180272739725571e+214, 6.6178770427031396e+218, 5.1885079009035934e+222, 4.1211451290267e+226, 3.251319971931891e+230, 2.604117443140602e+234, 2.0465756948134399e+238, 1.5761535814068614e+242, 1.1573462490590295e+246, 8.524508097806648e+249, 6.36559012879644e+253, 4.823679799507984e+257, 3.6038751987163943e+261, 2.7227117883999735e+265, 2.0556932432065202e+269, 1.586155703262089e+273, 1.2388237203568757e+277, 9.721587737340142e+280, 7.814470088844008e+284, 6.362198103387627e+288, 4.9803674279745565e+292, 3.8061175978996e+296, 2.7773049862774644e+300, 2.0533883857914577e+304, 1.478306756137644e+308, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity], "minus_di": [17.09, 137932.39, 1079370668.1, 8589488635265.79, 6.901671431762983e+16, 5.659420042263546e+20, 4.752397343403569e+24, 4.081917040001741e+28, 3.370434856264616e+32, 2.686486802534043e+36, 2.119879942028026e+40, 1.7161908649529624e+44, 1.3917158601513162e+48, 1.0994451625116657e+52, 8.553211493075441e+55, 6.768910523795183e+59, 5.3644595459138315e+63, 4.2286432431982e+67, 3.357332327140238e+71, 2.6380939013365783e+75, 2.0338122569145403e+79, 1.5418393373192597e+83, 1.1914444857658442e+87, 8.97819788371591e+90, 6.669788415602246e+94, 4.743455666646559e+98, 3.2881514666072643e+102, 2.316336597844769e+106, 1.6567131121660317e+110, 1.1809209069729514e+114, 8.53598716720872e+117, 6.1750492702729485e+121, 4.4359125428925705e+125, 3.259095074214236e+129, 2.385563042352507e+133, 1.7179745028293026e+137, 1.258782980521613e+141, 9.369717052595464e+144, 7.029666438465023e+148, 5.33801253096591e+152, 4.141216895847473e+156, 3.241164343821544e+160, 2.5624042806504705e+164, 2.0487821750961466e+168, 1.6066271379771542e+172, 1.274823039053362e+176, 1.0017762033716986e+180, 7.369482057197752e+183, 5.468413651805988e+187, 4.1995132224816654e+191, 3.236357911167221e+195, 2.552341656458476e+199, 2.0536876606120016e+203, 1.6447232975667595e+207, 1.2709611820069016e+211, 1.0067710047110491e+215, 8.14482222210105e+218, 6.385654217831335e+222, 5.072018445010244e+226, 4.001498212746368e+230, 3.20496640886351e+234, 2.518782849963586e+238, 1.9398200710666511e+242, 1.4243811704532635e+246, 1.0491370954685538e+250, 7.834325056699916e+253, 5.936649195779708e+257, 4.435398635359752e+261, 3.350924070580585e+265, 2.53000409361678e+269, 1.9521299861389548e+273, 1.5246579683667287e+277, 1.1964653215262967e+281, 9.61750561741522e+284, 7.830150388033611e+288, 6.129489417806797e+292, 4.6843044969358066e+296, 3.418113576879804e+300, 2.527167435611224e+304, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity], "current": {"adx": NaN, "plus_di": Infinity, "minus_di": Infinity}, "trend_strength": "very_strong"}, "ichimoku": {"tenkan": [0.14, 0.14, 0.13, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.13, 0.13, 0.13, 0.13, 0.13, 0.12, 0.12, 0.11, 0.11, 0.11, 0.11, 0.11, 0.11, 0.11, 0.11, 0.11, 0.12, 0.12, 0.13, 0.13, 0.14, 0.14, 0.15, 0.15, 0.15, 0.16, 0.16, 0.16, 0.16, 0.17, 0.18, 0.18, 0.19, 0.19, 0.19, 0.19, 0.2, 0.2, 0.2, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.18, 0.18, 0.18, 0.18, 0.19, 0.19, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.19, 0.19, 0.19, 0.18, 0.18, 0.18, 0.18, 0.17, 0.17, 0.17, 0.17, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.17, 0.17, 0.18, 0.18, 0.18, 0.18, 0.18, 0.19, 0.19, 0.19, 0.19, 0.19, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.19, 0.19, 0.19, 0.19, 0.18, 0.18, 0.18, 0.18, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.18, 0.18, 0.19, 0.19, 0.19, 0.2, 0.2, 0.2, 0.2, 0.2, 0.21, 0.22, 0.22, 0.22, 0.22, 0.22, 0.22, 0.22, 0.22, 0.22, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.2, 0.2, 0.2, 0.19, 0.19, 0.19, 0.19, 0.18, 0.17, 0.16, 0.16, 0.16], "kijun": [0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.12, 0.13, 0.13, 0.13, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.15, 0.15, 0.15, 0.16, 0.16, 0.16, 0.16, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.18, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.17, 0.17, 0.17, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.19, 0.19, 0.18, 0.18, 0.18, 0.18, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.2, 0.2, 0.2, 0.2, 0.2, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.19, 0.19, 0.18, 0.18, 0.18], "senkou_a": [0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.12, 0.12, 0.12, 0.12, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.14, 0.14, 0.14, 0.15, 0.15, 0.16, 0.16, 0.16, 0.16, 0.17, 0.17, 0.17, 0.17, 0.18, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.18, 0.18, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.18, 0.18, 0.18, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.18, 0.18, 0.18, 0.18, 0.18, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.18, 0.18, 0.18, 0.18, 0.18, 0.19, 0.19, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.19, 0.19, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.21, 0.2, 0.2, 0.2, 0.2], "senkou_b": [0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.16, 0.16, 0.16, 0.16, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.17, 0.18, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19, 0.19], "chikou": [0.09, 0.09, 0.1, 0.11, 0.11, 0.12, 0.13, 0.12, 0.12, 0.13, 0.12, 0.13, 0.13, 0.13, 0.15, 0.16, 0.16, 0.17, 0.18, 0.17, 0.18, 0.18, 0.18, 0.18, 0.19, 0.2, 0.2, 0.19, 0.19, 0.2, 0.19, 0.19, 0.18, 0.17, 0.17, 0.17, 0.19, 0.19, 0.19, 0.18, 0.18, 0.18, 0.17, 0.17, 0.18, 0.18, 0.19, 0.19, 0.19, 0.18, 0.17, 0.18, 0.19, 0.18, 0.17, 0.17, 0.18, 0.17, 0.18, 0.18, 0.19, 0.19, 0.18, 0.18, 0.2, 0.19, 0.19, 0.19, 0.19, 0.2, 0.19, 0.19, 0.18, 0.17, 0.17, 0.17, 0.16, 0.16, 0.15, 0.15, 0.15, 0.16, 0.15, 0.15, 0.16, 0.16, 0.15, 0.15, 0.16, 0.16, 0.16, 0.15, 0.15, 0.15, 0.15, 0.16, 0.17, 0.18, 0.18, 0.19, 0.19, 0.19, 0.18, 0.18, 0.17, 0.17, 0.17, 0.18, 0.18, 0.19, 0.19, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.17, 0.17, 0.17, 0.18, 0.17, 0.16, 0.15, 0.16, 0.17, 0.17, 0.17, 0.17, 0.18, 0.18, 0.18, 0.18, 0.18, 0.19, 0.2, 0.2, 0.2, 0.21, 0.2, 0.21, 0.2, 0.21, 0.23, 0.22, 0.22, 0.21, 0.21, 0.21, 0.21, 0.21, 0.2, 0.21, 0.22, 0.21, 0.21, 0.21, 0.21, 0.2, 0.19, 0.18, 0.17, 0.17, 0.18, 0.18, 0.17, 0.16, 0.15, 0.14, 0.14, 0.14, 0.14], "current": {"tenkan": 0.161, "kijun": 0.18235, "senkou_a": 0.19835000000000003, "senkou_b": 0.18635000000000002, "chikou": 0.1403}, "cloud_signal": "below_cloud"}, "overall_signal": {"signal": "bearish", "confidence": 66.67, "breakdown": {"bullish": 0, "bearish": 2, "neutral": 1}, "recommendation": "sell"}}}