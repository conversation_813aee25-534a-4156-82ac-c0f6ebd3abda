{"asset": "XAUUSD_otc", "timeframe": 300, "timestamp": 1753230133.3822672, "last_update": "2025-07-23T03:22:13.382267", "indicators": {"asset": "XAUUSD_otc", "timeframe": 300, "last_update": "2025-07-23T03:22:13.356985", "candles_count": 199, "timestamps": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "rsi": {"values": [43.08, 44.26, 44.52, 46.7, 48.96, 57.12, 58.05, 51.85, 52.54, 47.75, 52.03, 46.82, 42.49, 38.81, 37.75, 39.12, 41.18, 40.53, 45.19, 45.26, 54.33, 57.44, 60.34, 59.05, 60.33, 52.49, 49.31, 51.74, 51.32, 49.94, 56.9, 57.05, 61.52, 63.85, 57.47, 57.68, 48.02, 42.92, 41.14, 44.42, 51.36, 49.14, 52.77, 57.61, 54.36, 59.42, 59.77, 62.98, 63.11, 54.21, 51.42, 57.08, 55.83, 56.19, 50.93, 54.77, 46.39, 47.44, 47.57, 54.71, 55.9, 59.9, 67.6, 65.27, 65.79, 61.06, 64.69, 66.33, 67.35, 71.31, 60.91, 59.16, 55.43, 57.87, 55.97, 50.17, 50.9, 46.56, 46.82, 39.21, 33.63, 38.9, 46.61, 47.08, 51.22, 51.77, 49.98, 48.19, 49.11, 50.56, 53.6, 65.11, 58.51, 57.54, 55.97, 50.24, 48.43, 55.1, 51.24, 52.35, 49.15, 43.01, 49.47, 48.69, 46.0, 51.45, 59.25, 62.86, 63.01, 55.98, 56.47, 60.38, 64.29, 58.38, 58.51, 49.89, 54.47, 59.47, 62.9, 64.33, 59.66, 60.8, 63.37, 65.43, 67.12, 64.39, 68.04, 57.18, 51.99, 48.82, 51.64, 48.19, 43.61, 43.02, 44.75, 47.13, 46.59, 47.65, 48.01, 53.12, 59.75, 60.38, 63.46, 63.94, 70.47, 59.41, 59.45, 54.06, 54.26, 57.03, 58.15, 58.6, 60.65, 61.97, 56.85, 63.95, 65.64, 65.15, 63.52, 71.98, 64.09, 59.6, 63.76, 66.9, 72.71, 74.82, 77.83, 78.5, 77.49, 78.29, 79.11, 79.82, 78.86, 79.86, 77.5, 74.21, 70.25, 70.41, 73.0, 63.15, 54.76, 44.18, 47.05, 41.67, 47.48], "current": 47.48, "signal": "neutral"}, "macd": {"macd_line": [0.05, 0.04, 0.01, 0.0, -0.02, -0.03, -0.04, -0.04, -0.04, -0.03, -0.01, 0.01, 0.03, 0.04, 0.04, 0.03, 0.03, 0.03, 0.03, 0.04, 0.05, 0.06, 0.07, 0.07, 0.07, 0.06, 0.04, 0.02, 0.01, 0.01, 0.01, 0.02, 0.03, 0.03, 0.05, 0.06, 0.07, 0.08, 0.07, 0.06, 0.06, 0.06, 0.06, 0.05, 0.05, 0.04, 0.03, 0.02, 0.02, 0.02, 0.03, 0.05, 0.07, 0.08, 0.09, 0.1, 0.11, 0.12, 0.13, 0.12, 0.12, 0.11, 0.11, 0.1, 0.08, 0.07, 0.06, 0.04, 0.02, -0.01, -0.02, -0.02, -0.03, -0.02, -0.01, -0.01, -0.01, -0.01, -0.01, 0.0, 0.03, 0.04, 0.05, 0.05, 0.04, 0.03, 0.03, 0.03, 0.03, 0.02, 0.01, 0.01, 0.01, 0.01, 0.01, 0.02, 0.04, 0.06, 0.06, 0.06, 0.06, 0.08, 0.09, 0.09, 0.08, 0.07, 0.07, 0.09, 0.11, 0.11, 0.11, 0.12, 0.13, 0.14, 0.14, 0.15, 0.14, 0.13, 0.11, 0.1, 0.08, 0.05, 0.03, 0.02, 0.01, 0.01, 0.0, -0.01, 0.0, 0.02, 0.04, 0.05, 0.06, 0.08, 0.09, 0.09, 0.08, 0.07, 0.07, 0.07, 0.07, 0.07, 0.08, 0.07, 0.08, 0.09, 0.09, 0.1, 0.12, 0.12, 0.12, 0.12, 0.13, 0.15, 0.17, 0.2, 0.22, 0.24, 0.25, 0.26, 0.27, 0.27, 0.27, 0.27, 0.26, 0.25, 0.24, 0.23, 0.21, 0.18, 0.14, 0.11, 0.07, 0.06], "signal_line": [-0.007777777777777777, -0.01, -0.01, -0.01, -0.0, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.03, 0.04, 0.05, 0.05, 0.05, 0.05, 0.05, 0.04, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.04, 0.05, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.05, 0.04, 0.04, 0.04, 0.04, 0.04, 0.05, 0.06, 0.07, 0.08, 0.09, 0.1, 0.11, 0.11, 0.11, 0.11, 0.11, 0.11, 0.1, 0.09, 0.08, 0.07, 0.06, 0.05, 0.04, 0.03, 0.02, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.02, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.04, 0.04, 0.04, 0.04, 0.05, 0.06, 0.07, 0.07, 0.07, 0.07, 0.07, 0.08, 0.09, 0.09, 0.1, 0.11, 0.12, 0.12, 0.13, 0.13, 0.13, 0.13, 0.12, 0.11, 0.1, 0.09, 0.08, 0.07, 0.06, 0.05, 0.04, 0.03, 0.03, 0.03, 0.03, 0.04, 0.05, 0.06, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.08, 0.09, 0.1, 0.1, 0.1, 0.11, 0.12, 0.13, 0.14, 0.16, 0.18, 0.19, 0.2, 0.21, 0.22, 0.23, 0.24, 0.24, 0.24, 0.24, 0.24, 0.23, 0.22, 0.2, 0.18, 0.16, 0.14], "histogram": [-0.03, -0.02, 0.0, 0.02, 0.03, 0.03, 0.02, 0.01, 0.01, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, -0.01, -0.02, -0.02, -0.02, -0.02, -0.01, 0.0, 0.0, 0.02, 0.02, 0.02, 0.02, 0.01, 0.0, 0.0, 0.0, 0.0, -0.01, -0.01, -0.02, -0.02, -0.02, -0.02, -0.02, -0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.01, 0.0, 0.0, -0.01, -0.02, -0.02, -0.02, -0.03, -0.04, -0.06, -0.06, -0.05, -0.05, -0.03, -0.02, -0.02, -0.02, -0.02, -0.02, -0.01, 0.02, 0.02, 0.02, 0.02, 0.01, 0.0, 0.0, 0.0, 0.0, -0.01, -0.02, -0.02, -0.02, -0.02, -0.02, -0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.03, 0.03, 0.02, 0.01, 0.0, 0.0, 0.02, 0.03, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.0, -0.02, -0.02, -0.03, -0.05, -0.06, -0.06, -0.06, -0.05, -0.05, -0.05, -0.03, -0.01, 0.01, 0.02, 0.02, 0.03, 0.03, 0.02, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.01, 0.0, 0.01, 0.02, 0.02, 0.02, 0.03, 0.02, 0.02, 0.02, 0.02, 0.03, 0.04, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.05, 0.04, 0.03, 0.02, 0.01, 0.0, -0.01, -0.02, -0.04, -0.06, -0.07, -0.09, -0.08], "current": {"macd": 0.06, "signal": 0.14, "histogram": -0.08}, "trend": "bearish"}, "moving_averages": {"sma_20": {"values": [3384.84, 3384.85, 3384.84, 3384.84, 3384.84, 3384.84, 3384.84, 3384.83, 3384.82, 3384.81, 3384.82, 3384.83, 3384.83, 3384.83, 3384.83, 3384.84, 3384.84, 3384.86, 3384.87, 3384.87, 3384.87, 3384.86, 3384.85, 3384.85, 3384.85, 3384.85, 3384.86, 3384.88, 3384.9, 3384.92, 3384.94, 3384.96, 3384.96, 3384.97, 3384.97, 3384.97, 3384.97, 3384.96, 3384.97, 3384.96, 3384.97, 3384.99, 3385.0, 3385.02, 3385.03, 3385.03, 3385.04, 3385.04, 3385.04, 3385.04, 3385.04, 3385.04, 3385.05, 3385.06, 3385.07, 3385.08, 3385.1, 3385.12, 3385.14, 3385.16, 3385.17, 3385.18, 3385.19, 3385.21, 3385.23, 3385.25, 3385.27, 3385.28, 3385.29, 3385.31, 3385.32, 3385.34, 3385.35, 3385.36, 3385.36, 3385.35, 3385.34, 3385.32, 3385.31, 3385.31, 3385.3, 3385.29, 3385.28, 3385.26, 3385.24, 3385.24, 3385.24, 3385.24, 3385.24, 3385.24, 3385.24, 3385.24, 3385.24, 3385.25, 3385.26, 3385.28, 3385.28, 3385.29, 3385.29, 3385.29, 3385.29, 3385.31, 3385.32, 3385.34, 3385.35, 3385.36, 3385.36, 3385.38, 3385.38, 3385.39, 3385.4, 3385.41, 3385.43, 3385.45, 3385.47, 3385.49, 3385.52, 3385.55, 3385.58, 3385.61, 3385.64, 3385.66, 3385.68, 3385.68, 3385.69, 3385.7, 3385.7, 3385.69, 3385.69, 3385.69, 3385.69, 3385.69, 3385.69, 3385.68, 3385.68, 3385.68, 3385.68, 3385.69, 3385.69, 3385.69, 3385.7, 3385.69, 3385.69, 3385.7, 3385.71, 3385.72, 3385.73, 3385.76, 3385.78, 3385.8, 3385.82, 3385.85, 3385.88, 3385.9, 3385.93, 3385.95, 3385.96, 3385.98, 3386.0, 3386.02, 3386.05, 3386.1, 3386.14, 3386.19, 3386.24, 3386.28, 3386.33, 3386.37, 3386.42, 3386.47, 3386.51, 3386.54, 3386.58, 3386.62, 3386.64, 3386.66, 3386.68, 3386.69, 3386.69, 3386.69], "current": 3386.69}, "ema_20": {"values": [3384.83915, 3384.86, 3384.87, 3384.88, 3384.88, 3384.89, 3384.89, 3384.88, 3384.86, 3384.84, 3384.82, 3384.81, 3384.8, 3384.8, 3384.8, 3384.81, 3384.83, 3384.85, 3384.87, 3384.89, 3384.89, 3384.89, 3384.89, 3384.89, 3384.89, 3384.9, 3384.91, 3384.93, 3384.95, 3384.96, 3384.97, 3384.97, 3384.96, 3384.94, 3384.93, 3384.93, 3384.93, 3384.93, 3384.94, 3384.95, 3384.97, 3384.99, 3385.01, 3385.03, 3385.04, 3385.04, 3385.05, 3385.06, 3385.07, 3385.07, 3385.08, 3385.07, 3385.06, 3385.05, 3385.06, 3385.07, 3385.08, 3385.11, 3385.14, 3385.17, 3385.19, 3385.21, 3385.23, 3385.26, 3385.29, 3385.31, 3385.32, 3385.32, 3385.33, 3385.34, 3385.34, 3385.34, 3385.33, 3385.32, 3385.3, 3385.27, 3385.25, 3385.24, 3385.23, 3385.23, 3385.23, 3385.23, 3385.23, 3385.23, 3385.23, 3385.23, 3385.26, 3385.28, 3385.29, 3385.3, 3385.3, 3385.3, 3385.31, 3385.31, 3385.31, 3385.31, 3385.3, 3385.3, 3385.3, 3385.29, 3385.29, 3385.31, 3385.33, 3385.35, 3385.36, 3385.37, 3385.39, 3385.42, 3385.43, 3385.44, 3385.44, 3385.45, 3385.47, 3385.5, 3385.53, 3385.55, 3385.57, 3385.59, 3385.62, 3385.65, 3385.67, 3385.7, 3385.71, 3385.71, 3385.7, 3385.7, 3385.69, 3385.67, 3385.65, 3385.64, 3385.63, 3385.62, 3385.61, 3385.61, 3385.61, 3385.63, 3385.65, 3385.67, 3385.69, 3385.72, 3385.74, 3385.75, 3385.75, 3385.75, 3385.76, 3385.77, 3385.78, 3385.79, 3385.81, 3385.82, 3385.84, 3385.86, 3385.88, 3385.9, 3385.93, 3385.95, 3385.96, 3385.98, 3386.01, 3386.05, 3386.1, 3386.16, 3386.22, 3386.27, 3386.32, 3386.37, 3386.41, 3386.45, 3386.49, 3386.52, 3386.55, 3386.57, 3386.59, 3386.61, 3386.62, 3386.62, 3386.6, 3386.59, 3386.57, 3386.56], "current": 3386.56}, "sma_50": {"values": [3384.88, 3384.88, 3384.88, 3384.88, 3384.87, 3384.87, 3384.87, 3384.87, 3384.88, 3384.88, 3384.89, 3384.91, 3384.92, 3384.93, 3384.93, 3384.94, 3384.95, 3384.95, 3384.96, 3384.96, 3384.96, 3384.96, 3384.96, 3384.96, 3384.97, 3384.97, 3384.98, 3384.99, 3385.0, 3385.02, 3385.03, 3385.05, 3385.06, 3385.08, 3385.1, 3385.11, 3385.12, 3385.12, 3385.13, 3385.14, 3385.14, 3385.15, 3385.16, 3385.17, 3385.17, 3385.17, 3385.17, 3385.17, 3385.17, 3385.17, 3385.18, 3385.18, 3385.19, 3385.2, 3385.21, 3385.21, 3385.22, 3385.23, 3385.24, 3385.25, 3385.25, 3385.25, 3385.26, 3385.26, 3385.26, 3385.27, 3385.27, 3385.27, 3385.27, 3385.28, 3385.28, 3385.29, 3385.3, 3385.31, 3385.32, 3385.32, 3385.33, 3385.34, 3385.34, 3385.34, 3385.34, 3385.35, 3385.35, 3385.35, 3385.36, 3385.36, 3385.37, 3385.38, 3385.38, 3385.39, 3385.41, 3385.42, 3385.43, 3385.44, 3385.45, 3385.46, 3385.48, 3385.48, 3385.49, 3385.49, 3385.5, 3385.51, 3385.51, 3385.52, 3385.53, 3385.54, 3385.54, 3385.55, 3385.56, 3385.58, 3385.59, 3385.6, 3385.61, 3385.62, 3385.63, 3385.64, 3385.66, 3385.67, 3385.68, 3385.7, 3385.71, 3385.72, 3385.73, 3385.74, 3385.76, 3385.77, 3385.78, 3385.79, 3385.81, 3385.83, 3385.85, 3385.87, 3385.9, 3385.92, 3385.94, 3385.96, 3385.98, 3386.0, 3386.02, 3386.04, 3386.06, 3386.07, 3386.09, 3386.12, 3386.14, 3386.16, 3386.17, 3386.19, 3386.21, 3386.23], "current": 3386.23}, "ema_50": {"values": [3384.88374, 3384.89, 3384.89, 3384.89, 3384.89, 3384.89, 3384.89, 3384.89, 3384.9, 3384.9, 3384.91, 3384.92, 3384.93, 3384.94, 3384.95, 3384.95, 3384.96, 3384.97, 3384.98, 3384.98, 3384.99, 3384.99, 3384.99, 3384.99, 3385.0, 3385.01, 3385.02, 3385.04, 3385.05, 3385.06, 3385.07, 3385.08, 3385.1, 3385.12, 3385.14, 3385.15, 3385.16, 3385.17, 3385.18, 3385.19, 3385.19, 3385.19, 3385.19, 3385.19, 3385.19, 3385.18, 3385.17, 3385.17, 3385.17, 3385.17, 3385.17, 3385.17, 3385.17, 3385.17, 3385.17, 3385.17, 3385.18, 3385.19, 3385.2, 3385.21, 3385.21, 3385.21, 3385.22, 3385.22, 3385.22, 3385.22, 3385.22, 3385.22, 3385.22, 3385.22, 3385.22, 3385.23, 3385.24, 3385.25, 3385.26, 3385.27, 3385.28, 3385.29, 3385.3, 3385.31, 3385.31, 3385.32, 3385.33, 3385.35, 3385.37, 3385.38, 3385.39, 3385.41, 3385.43, 3385.45, 3385.47, 3385.49, 3385.5, 3385.51, 3385.51, 3385.52, 3385.52, 3385.52, 3385.52, 3385.52, 3385.52, 3385.52, 3385.52, 3385.52, 3385.53, 3385.54, 3385.55, 3385.56, 3385.57, 3385.59, 3385.6, 3385.61, 3385.62, 3385.63, 3385.64, 3385.65, 3385.66, 3385.67, 3385.68, 3385.69, 3385.7, 3385.71, 3385.72, 3385.73, 3385.75, 3385.77, 3385.78, 3385.8, 3385.82, 3385.85, 3385.88, 3385.91, 3385.94, 3385.97, 3386.0, 3386.03, 3386.06, 3386.09, 3386.12, 3386.15, 3386.18, 3386.2, 3386.22, 3386.24, 3386.26, 3386.27, 3386.28, 3386.29, 3386.29, 3386.3], "current": 3386.3}, "trend": "bearish"}, "bollinger_bands": {"upper": [3385.15, 3385.17, 3385.16, 3385.16, 3385.15, 3385.15, 3385.15, 3385.14, 3385.14, 3385.14, 3385.14, 3385.12, 3385.1, 3385.1, 3385.1, 3385.11, 3385.12, 3385.15, 3385.17, 3385.18, 3385.17, 3385.14, 3385.13, 3385.12, 3385.12, 3385.13, 3385.15, 3385.18, 3385.21, 3385.21, 3385.21, 3385.21, 3385.18, 3385.18, 3385.17, 3385.17, 3385.17, 3385.16, 3385.17, 3385.16, 3385.18, 3385.21, 3385.24, 3385.27, 3385.27, 3385.27, 3385.29, 3385.29, 3385.29, 3385.29, 3385.29, 3385.29, 3385.28, 3385.26, 3385.24, 3385.24, 3385.25, 3385.32, 3385.37, 3385.41, 3385.43, 3385.47, 3385.5, 3385.55, 3385.61, 3385.63, 3385.65, 3385.66, 3385.67, 3385.67, 3385.67, 3385.66, 3385.63, 3385.6, 3385.61, 3385.64, 3385.66, 3385.65, 3385.65, 3385.64, 3385.63, 3385.62, 3385.6, 3385.57, 3385.5, 3385.48, 3385.5, 3385.51, 3385.51, 3385.5, 3385.5, 3385.5, 3385.51, 3385.52, 3385.52, 3385.5, 3385.47, 3385.47, 3385.46, 3385.47, 3385.47, 3385.5, 3385.53, 3385.57, 3385.58, 3385.59, 3385.6, 3385.65, 3385.66, 3385.68, 3385.69, 3385.69, 3385.73, 3385.77, 3385.82, 3385.84, 3385.85, 3385.88, 3385.91, 3385.92, 3385.93, 3385.98, 3386.0, 3385.99, 3385.99, 3385.98, 3385.98, 3385.98, 3385.99, 3386.0, 3385.98, 3385.97, 3385.98, 3385.97, 3385.97, 3385.97, 3385.98, 3385.99, 3386.0, 3386.02, 3386.03, 3386.01, 3386.01, 3386.03, 3386.04, 3386.06, 3386.07, 3386.09, 3386.09, 3386.09, 3386.1, 3386.12, 3386.13, 3386.12, 3386.17, 3386.2, 3386.2, 3386.24, 3386.28, 3386.37, 3386.47, 3386.6, 3386.69, 3386.77, 3386.85, 3386.92, 3386.98, 3387.03, 3387.09, 3387.11, 3387.14, 3387.14, 3387.15, 3387.14, 3387.14, 3387.11, 3387.06, 3387.02, 3387.0, 3387.0], "middle": [3384.84, 3384.85, 3384.84, 3384.84, 3384.84, 3384.84, 3384.84, 3384.83, 3384.82, 3384.81, 3384.82, 3384.83, 3384.83, 3384.83, 3384.83, 3384.84, 3384.84, 3384.86, 3384.87, 3384.87, 3384.87, 3384.86, 3384.85, 3384.85, 3384.85, 3384.85, 3384.86, 3384.88, 3384.9, 3384.92, 3384.94, 3384.96, 3384.96, 3384.97, 3384.97, 3384.97, 3384.97, 3384.96, 3384.97, 3384.96, 3384.97, 3384.99, 3385.0, 3385.02, 3385.03, 3385.03, 3385.04, 3385.04, 3385.04, 3385.04, 3385.04, 3385.04, 3385.05, 3385.06, 3385.07, 3385.08, 3385.1, 3385.12, 3385.14, 3385.16, 3385.17, 3385.18, 3385.19, 3385.21, 3385.23, 3385.25, 3385.27, 3385.28, 3385.29, 3385.31, 3385.32, 3385.34, 3385.35, 3385.36, 3385.36, 3385.35, 3385.34, 3385.32, 3385.31, 3385.31, 3385.3, 3385.29, 3385.28, 3385.26, 3385.24, 3385.24, 3385.24, 3385.24, 3385.24, 3385.24, 3385.24, 3385.24, 3385.24, 3385.25, 3385.26, 3385.28, 3385.28, 3385.29, 3385.29, 3385.29, 3385.29, 3385.31, 3385.32, 3385.34, 3385.35, 3385.36, 3385.36, 3385.38, 3385.38, 3385.39, 3385.4, 3385.41, 3385.43, 3385.45, 3385.47, 3385.49, 3385.52, 3385.55, 3385.58, 3385.61, 3385.64, 3385.66, 3385.68, 3385.68, 3385.69, 3385.7, 3385.7, 3385.69, 3385.69, 3385.69, 3385.69, 3385.69, 3385.69, 3385.68, 3385.68, 3385.68, 3385.68, 3385.69, 3385.69, 3385.69, 3385.7, 3385.69, 3385.69, 3385.7, 3385.71, 3385.72, 3385.73, 3385.76, 3385.78, 3385.8, 3385.82, 3385.85, 3385.88, 3385.9, 3385.93, 3385.95, 3385.96, 3385.98, 3386.0, 3386.02, 3386.05, 3386.1, 3386.14, 3386.19, 3386.24, 3386.28, 3386.33, 3386.37, 3386.42, 3386.47, 3386.51, 3386.54, 3386.58, 3386.62, 3386.64, 3386.66, 3386.68, 3386.69, 3386.69, 3386.69], "lower": [3384.53, 3384.53, 3384.52, 3384.52, 3384.53, 3384.53, 3384.53, 3384.52, 3384.5, 3384.48, 3384.5, 3384.54, 3384.56, 3384.56, 3384.56, 3384.57, 3384.56, 3384.57, 3384.57, 3384.56, 3384.57, 3384.58, 3384.57, 3384.58, 3384.58, 3384.57, 3384.57, 3384.58, 3384.59, 3384.63, 3384.67, 3384.71, 3384.74, 3384.76, 3384.77, 3384.77, 3384.77, 3384.76, 3384.77, 3384.76, 3384.76, 3384.77, 3384.76, 3384.77, 3384.79, 3384.79, 3384.79, 3384.79, 3384.79, 3384.79, 3384.79, 3384.79, 3384.82, 3384.86, 3384.9, 3384.92, 3384.95, 3384.92, 3384.91, 3384.91, 3384.91, 3384.89, 3384.88, 3384.87, 3384.85, 3384.87, 3384.89, 3384.9, 3384.91, 3384.95, 3384.97, 3385.02, 3385.07, 3385.12, 3385.11, 3385.06, 3385.02, 3384.99, 3384.97, 3384.98, 3384.97, 3384.96, 3384.96, 3384.95, 3384.98, 3385.0, 3384.98, 3384.97, 3384.97, 3384.98, 3384.98, 3384.98, 3384.97, 3384.98, 3385.0, 3385.06, 3385.09, 3385.11, 3385.12, 3385.11, 3385.11, 3385.12, 3385.11, 3385.11, 3385.12, 3385.13, 3385.12, 3385.11, 3385.1, 3385.1, 3385.11, 3385.13, 3385.13, 3385.13, 3385.12, 3385.14, 3385.19, 3385.22, 3385.25, 3385.3, 3385.35, 3385.34, 3385.36, 3385.37, 3385.39, 3385.42, 3385.42, 3385.4, 3385.39, 3385.38, 3385.4, 3385.41, 3385.4, 3385.39, 3385.39, 3385.39, 3385.38, 3385.39, 3385.38, 3385.36, 3385.37, 3385.37, 3385.37, 3385.37, 3385.38, 3385.38, 3385.39, 3385.43, 3385.47, 3385.51, 3385.54, 3385.58, 3385.63, 3385.68, 3385.69, 3385.7, 3385.72, 3385.72, 3385.72, 3385.67, 3385.63, 3385.6, 3385.59, 3385.61, 3385.63, 3385.64, 3385.68, 3385.71, 3385.75, 3385.83, 3385.88, 3385.94, 3386.01, 3386.1, 3386.14, 3386.21, 3386.3, 3386.36, 3386.38, 3386.38], "current": {"upper": 3387.0, "middle": 3386.69, "lower": 3386.38}, "position": "within_bands"}, "stochastic": {"k_values": [61.01, 52.03, 55.51, 56.23, 62.17, 81.8, 96.3, 88.8, 73.25, 75.23, 62.45, 74.04, 50.96, 11.18, 1.16, 6.38, 11.56, 20.2, 17.73, 30.05, 30.21, 64.09, 90.61, 99.79, 79.38, 84.19, 62.89, 53.09, 60.65, 59.45, 55.67, 75.23, 73.55, 90.51, 90.63, 64.85, 65.94, 24.52, 8.72, 21.7, 31.13, 53.58, 46.04, 58.49, 76.98, 66.98, 87.55, 89.06, 94.79, 87.0, 66.09, 58.64, 76.39, 70.66, 66.53, 40.92, 57.63, 8.76, 13.84, 14.41, 49.72, 56.21, 79.1, 97.95, 90.97, 91.77, 78.31, 95.23, 88.62, 93.21, 93.91, 72.37, 68.02, 59.16, 62.02, 54.72, 18.68, 14.85, 11.92, 18.71, 0.0, 1.93, 14.13, 32.74, 34.82, 52.88, 54.65, 56.28, 50.46, 58.63, 72.08, 83.3, 100.0, 79.4, 76.89, 72.96, 50.64, 30.57, 57.49, 38.85, 44.11, 30.08, 0.5, 29.28, 25.56, 17.76, 40.65, 100.0, 95.69, 83.27, 61.15, 63.2, 80.3, 98.17, 70.14, 70.63, 44.78, 61.16, 81.7, 97.55, 96.39, 72.93, 79.69, 93.56, 99.0, 91.32, 84.08, 100.0, 67.98, 50.41, 38.75, 41.06, 11.06, 0.0, 0.0, 9.26, 17.58, 15.5, 18.9, 20.04, 36.29, 61.63, 91.15, 88.55, 90.93, 98.84, 71.26, 71.43, 56.31, 55.65, 65.24, 69.18, 69.52, 74.51, 76.51, 46.71, 91.85, 94.07, 83.21, 78.37, 99.24, 69.22, 58.0, 74.49, 87.84, 100.0, 97.58, 99.66, 98.48, 93.45, 97.13, 98.99, 95.69, 94.41, 97.37, 94.51, 89.79, 82.08, 80.29, 88.3, 53.63, 2.75, 4.01, 17.21, 6.97, 25.17], "d_values": [56.18, 54.59, 57.97, 66.73, 80.09, 88.97, 86.12, 79.09, 70.31, 70.57, 62.48, 45.39, 21.1, 6.24, 6.37, 12.71, 16.5, 22.66, 26.0, 41.45, 61.64, 84.83, 89.93, 87.79, 75.49, 66.72, 58.88, 57.73, 58.59, 63.45, 68.15, 79.76, 84.9, 82.0, 73.81, 51.77, 33.06, 18.31, 20.52, 35.47, 43.58, 52.7, 60.5, 67.48, 77.17, 81.2, 90.47, 90.28, 82.63, 70.58, 67.04, 68.56, 71.19, 59.37, 55.03, 35.77, 26.74, 12.34, 25.99, 40.11, 61.68, 77.75, 89.34, 93.56, 87.02, 88.44, 87.39, 92.35, 91.91, 86.5, 78.1, 66.52, 63.07, 58.63, 45.14, 29.42, 15.15, 15.16, 10.21, 6.88, 5.35, 16.27, 27.23, 40.15, 47.45, 54.6, 53.8, 55.12, 60.39, 71.34, 85.13, 87.57, 85.43, 76.42, 66.83, 51.39, 46.23, 42.3, 46.82, 37.68, 24.9, 19.95, 18.45, 24.2, 27.99, 52.8, 78.78, 92.99, 80.04, 69.21, 68.22, 80.56, 82.87, 79.65, 61.85, 58.86, 62.55, 80.14, 91.88, 88.96, 83.0, 82.06, 90.75, 94.63, 91.47, 91.8, 84.02, 72.8, 52.38, 43.41, 30.29, 17.37, 3.69, 3.09, 8.95, 14.11, 17.33, 18.15, 25.08, 39.32, 63.02, 80.44, 90.21, 92.77, 87.01, 80.51, 66.33, 61.13, 59.07, 63.36, 67.98, 71.07, 73.51, 65.91, 71.69, 77.54, 89.71, 85.22, 86.94, 82.28, 75.49, 67.24, 73.44, 87.44, 95.14, 99.08, 98.57, 97.2, 96.35, 96.52, 97.27, 96.36, 95.82, 95.43, 93.89, 88.79, 84.05, 83.56, 74.07, 48.23, 20.13, 7.99, 9.4, 16.45], "current": {"k": 25.17, "d": 16.45}, "signal": "neutral"}, "atr": {"values": [0.15314285714290626, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.14, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.14, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.13, 0.14, 0.14, 0.14, 0.14], "current": 0.14, "volatility": "high"}, "adx": {"adx": [3.8222322965636915, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, 3.82, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN], "plus_di": [25.23, 15718.93, 9901470.99, 6377342234.07, 4214363171323.96, 2679454724130741.0, 1.726412656857923e+18, 1.1140748839030038e+21, 7.264608851498824e+23, 4.686305172315691e+26, 3.0733176132494856e+29, 1.993972762019844e+32, 1.292480306048568e+35, 8.53634410810307e+37, 5.732865599681697e+40, 3.9695823371409843e+43, 2.7502575112608135e+46, 1.9459416831324087e+49, 1.391417758388215e+52, 1.0126611384025184e+55, 7.063916115050444e+57, 5.01939187082962e+60, 3.625433718084662e+63, 2.631111773251291e+66, 1.9397608333401527e+69, 1.3822161625978813e+72, 9.802586831868322e+74, 7.182860765382066e+77, 5.146419321010134e+80, 3.723457058986409e+83, 2.673617108185335e+86, 1.9329350516746085e+89, 1.4009053187689555e+92, 9.921499686662454e+94, 6.928894205885759e+97, 4.854492444820045e+100, 3.3568759821852854e+103, 2.2803787791920146e+106, 1.5360079066117035e+109, 1.0063852022520359e+112, 6.559816298586186e+114, 4.3509908494296117e+117, 2.956822918734792e+120, 1.9841157984696077e+123, 1.3316617617489888e+126, 8.847851761876457e+128, 6.003766271887883e+131, 3.963914883798027e+134, 2.58514687694663e+137, 1.6879724693074456e+140, 1.117434788205675e+143, 7.301720359263033e+145, 4.819917081315461e+148, 3.189223588734491e+151, 2.0988410338159112e+154, 1.380625380654445e+157, 8.91119161218041e+159, 5.940044980725489e+162, 4.072938666600137e+165, 2.7021030582516633e+168, 1.8318839638928072e+171, 1.2335378269503774e+174, 7.96745941276607e+176, 5.298265020758826e+179, 3.62649315233854e+182, 2.5256540042604926e+185, 1.778348500900974e+188, 1.2534351377531578e+191, 8.896066030632728e+193, 6.211547894687577e+196, 4.234743400076971e+199, 2.8559031712923297e+202, 1.89040621690407e+205, 1.2735462717584137e+208, 8.863771537965936e+210, 6.131087789003226e+213, 4.3885648103475125e+216, 3.058313903019636e+219, 2.1384459764892346e+222, 1.4690202007309688e+225, 9.97125118408042e+227, 6.6768446036710375e+230, 4.444946529605878e+233, 2.9387719883295117e+236, 1.9633842662314219e+239, 1.3260333255393664e+242, 9.090323927227366e+244, 6.220035232563142e+247, 4.321340339982841e+250, 2.9558774703988895e+253, 2.036612404350679e+256, 1.3048705703653477e+259, 8.405666418225948e+261, 5.575378783950503e+264, 3.7633583254538683e+267, 2.5304855166243656e+270, 1.7421379218659272e+273, 1.1967787923419364e+276, 8.274608693922821e+278, 5.768061872434443e+281, 4.016940752872295e+284, 2.7964474244287798e+287, 1.9629560666052444e+290, 1.4062001623926383e+293, 1.0043811539364414e+296, 7.103583704105003e+298, 4.83796845711265e+301, 3.3216459754502827e+304, 2.288119437287631e+307, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity], "minus_di": [27.24, 16968.31, 10688466.58, 6884230575.51, 4549332109749.63, 2892425004103008.0, 1.8636325858111836e+18, 1.2026245570130307e+21, 7.842019534000518e+23, 5.0587853324002545e+26, 3.3175931767224906e+29, 2.152459088943023e+32, 1.3952101227380824e+35, 9.214835734876913e+37, 6.188529201985587e+40, 4.2850954354218836e+43, 2.968855387498138e+46, 2.1006103705090393e+49, 1.502011390328929e+52, 1.0931501738097447e+55, 7.625375198189443e+57, 5.41834665905352e+60, 3.913592956980507e+63, 2.840239625250896e+66, 2.093938250123976e+69, 1.4920784269159178e+72, 1.058172284160729e+75, 7.75377388976841e+77, 5.555470593188573e+80, 4.019407457093734e+83, 2.88612339871562e+86, 2.0865699369428778e+89, 1.5122530475683853e+92, 1.0710087210453788e+95, 7.479621383932937e+97, 5.240340582422734e+100, 3.6236895287325616e+103, 2.461629427942906e+106, 1.6580939530616327e+109, 1.0863754093465387e+112, 7.081208170258793e+114, 4.696819323788479e+117, 3.1918391700490046e+120, 2.1418186673749802e+123, 1.4375058260931066e+126, 9.551102856179329e+128, 6.480961789429916e+131, 4.2789775176190076e+134, 2.7906213151576974e+137, 1.822137068595649e+140, 1.2062515155613192e+143, 7.882080764380879e+145, 5.203017075880167e+148, 3.442711671391789e+151, 2.2656625734984178e+154, 1.4903612053380227e+157, 9.619477128424117e+159, 6.412175758379653e+162, 4.396666804390228e+165, 2.9168734888348738e+168, 1.9774870364707644e+171, 1.3315827366090882e+174, 8.60073455136911e+176, 5.71938539822829e+179, 3.914736597923017e+182, 2.726399788675617e+185, 1.919696430654857e+188, 1.35306153927818e+191, 9.603149324906441e+193, 6.705258455377094e+196, 4.571332213988611e+199, 3.0828980492002347e+202, 2.040660795811346e+205, 1.3747711603939245e+208, 9.568287978960563e+210, 6.618403163732007e+213, 4.737379764345052e+216, 3.301396931183314e+219, 2.308415423711834e+222, 1.585781883851436e+225, 1.0763793090917958e+228, 7.207538200308118e+230, 4.798242854543422e+233, 3.17235350306559e+236, 2.119439337103391e+239, 1.4314300266104382e+242, 9.812847362456633e+244, 6.714420389732051e+247, 4.664812111971886e+250, 3.190818574932313e+253, 2.198487844918264e+256, 1.4085851986423522e+259, 9.07377143016036e+261, 6.018524909867821e+264, 4.06248018371475e+267, 2.731615322658353e+270, 1.8806077372461677e+273, 1.2919019949004813e+276, 8.932296884857801e+278, 6.226522969316111e+281, 4.336218025619933e+284, 3.0187165994011402e+287, 2.1189771030173484e+290, 1.5179687396276833e+293, 1.0842120738718807e+296, 7.668195674087253e+298, 5.222503223656449e+301, 3.585659346152005e+304, 2.469985334397435e+307, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity], "current": {"adx": NaN, "plus_di": Infinity, "minus_di": Infinity}, "trend_strength": "very_strong"}, "ichimoku": {"tenkan": [3384.89, 3384.86, 3384.79, 3384.76, 3384.76, 3384.71, 3384.68, 3384.68, 3384.65, 3384.65, 3384.67, 3384.76, 3384.86, 3384.94, 3384.94, 3384.94, 3384.94, 3384.98, 3384.97, 3384.92, 3384.89, 3384.85, 3384.8, 3384.79, 3384.78, 3384.77, 3384.76, 3384.78, 3384.81, 3384.86, 3384.86, 3384.88, 3384.9, 3384.92, 3384.95, 3384.99, 3384.99, 3384.99, 3384.98, 3385.02, 3385.02, 3385.02, 3385.04, 3384.99, 3384.93, 3384.93, 3384.93, 3384.93, 3384.93, 3384.9, 3384.9, 3384.91, 3384.92, 3384.99, 3385.06, 3385.09, 3385.09, 3385.13, 3385.13, 3385.15, 3385.15, 3385.15, 3385.12, 3385.1, 3385.1, 3385.1, 3385.1, 3385.1, 3385.19, 3385.19, 3385.2, 3385.2, 3385.21, 3385.25, 3385.29, 3385.37, 3385.43, 3385.48, 3385.48, 3385.48, 3385.48, 3385.46, 3385.45, 3385.41, 3385.39, 3385.31, 3385.23, 3385.19, 3385.19, 3385.17, 3385.14, 3385.14, 3385.12, 3385.12, 3385.12, 3385.13, 3385.17, 3385.32, 3385.35, 3385.35, 3385.35, 3385.35, 3385.36, 3385.36, 3385.36, 3385.38, 3385.38, 3385.3, 3385.3, 3385.27, 3385.26, 3385.26, 3385.3, 3385.36, 3385.39, 3385.39, 3385.39, 3385.39, 3385.4, 3385.46, 3385.51, 3385.55, 3385.55, 3385.55, 3385.55, 3385.58, 3385.6, 3385.6, 3385.6, 3385.62, 3385.67, 3385.71, 3385.79, 3385.83, 3385.83, 3385.77, 3385.77, 3385.77, 3385.74, 3385.73, 3385.72, 3385.72, 3385.64, 3385.58, 3385.58, 3385.58, 3385.62, 3385.64, 3385.69, 3385.7, 3385.77, 3385.77, 3385.78, 3385.8, 3385.83, 3385.9, 3385.9, 3385.9, 3385.9, 3385.9, 3385.87, 3385.89, 3385.92, 3385.94, 3385.98, 3386.05, 3386.09, 3386.09, 3386.09, 3386.11, 3386.23, 3386.3, 3386.36, 3386.39, 3386.4, 3386.4, 3386.44, 3386.53, 3386.57, 3386.66, 3386.71, 3386.78, 3386.78, 3386.77, 3386.77, 3386.77, 3386.75, 3386.64, 3386.62, 3386.58, 3386.58], "kijun": [3384.8, 3384.8, 3384.8, 3384.8, 3384.8, 3384.8, 3384.8, 3384.8, 3384.8, 3384.8, 3384.8, 3384.8, 3384.86, 3384.87, 3384.87, 3384.87, 3384.87, 3384.87, 3384.87, 3384.87, 3384.87, 3384.86, 3384.88, 3384.88, 3384.88, 3384.88, 3384.88, 3384.88, 3384.88, 3384.88, 3384.88, 3384.9, 3384.93, 3384.93, 3384.93, 3384.93, 3384.96, 3384.99, 3384.99, 3384.99, 3384.99, 3384.99, 3384.99, 3384.99, 3384.99, 3384.99, 3384.99, 3384.99, 3384.99, 3384.99, 3384.99, 3385.05, 3385.05, 3385.09, 3385.13, 3385.17, 3385.21, 3385.24, 3385.3, 3385.3, 3385.3, 3385.3, 3385.3, 3385.3, 3385.3, 3385.3, 3385.3, 3385.3, 3385.3, 3385.29, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.23, 3385.23, 3385.23, 3385.23, 3385.23, 3385.23, 3385.23, 3385.23, 3385.23, 3385.23, 3385.23, 3385.3, 3385.39, 3385.39, 3385.39, 3385.39, 3385.4, 3385.44, 3385.44, 3385.44, 3385.44, 3385.44, 3385.44, 3385.46, 3385.47, 3385.47, 3385.48, 3385.49, 3385.54, 3385.54, 3385.55, 3385.56, 3385.56, 3385.56, 3385.58, 3385.63, 3385.67, 3385.67, 3385.67, 3385.67, 3385.67, 3385.68, 3385.68, 3385.68, 3385.68, 3385.68, 3385.72, 3385.72, 3385.76, 3385.76, 3385.76, 3385.76, 3385.76, 3385.76, 3385.76, 3385.76, 3385.76, 3385.76, 3385.76, 3385.76, 3385.77, 3385.79, 3385.79, 3385.86, 3385.91, 3385.91, 3385.91, 3385.92, 3386.01, 3386.1, 3386.22, 3386.25, 3386.26, 3386.26, 3386.28, 3386.31, 3386.31, 3386.32, 3386.32, 3386.36, 3386.36, 3386.36, 3386.36, 3386.36, 3386.38, 3386.44, 3386.45, 3386.45, 3386.45], "senkou_a": [3384.84, 3384.83, 3384.79, 3384.78, 3384.78, 3384.75, 3384.74, 3384.74, 3384.72, 3384.72, 3384.73, 3384.78, 3384.86, 3384.91, 3384.91, 3384.91, 3384.91, 3384.93, 3384.92, 3384.9, 3384.88, 3384.85, 3384.84, 3384.84, 3384.83, 3384.83, 3384.82, 3384.83, 3384.85, 3384.87, 3384.87, 3384.89, 3384.91, 3384.93, 3384.94, 3384.96, 3384.97, 3384.99, 3384.98, 3385.0, 3385.0, 3385.01, 3385.01, 3384.99, 3384.96, 3384.96, 3384.96, 3384.96, 3384.96, 3384.94, 3384.94, 3384.98, 3384.99, 3385.04, 3385.1, 3385.13, 3385.15, 3385.18, 3385.21, 3385.22, 3385.22, 3385.22, 3385.21, 3385.2, 3385.2, 3385.2, 3385.2, 3385.2, 3385.25, 3385.24, 3385.24, 3385.24, 3385.25, 3385.27, 3385.29, 3385.33, 3385.36, 3385.38, 3385.38, 3385.38, 3385.38, 3385.37, 3385.37, 3385.35, 3385.33, 3385.27, 3385.23, 3385.21, 3385.21, 3385.2, 3385.19, 3385.19, 3385.18, 3385.18, 3385.18, 3385.18, 3385.24, 3385.36, 3385.37, 3385.37, 3385.37, 3385.38, 3385.4, 3385.4, 3385.4, 3385.41, 3385.41, 3385.37, 3385.38, 3385.37, 3385.36, 3385.37, 3385.4, 3385.45, 3385.47, 3385.47, 3385.47, 3385.47, 3385.48, 3385.52, 3385.57, 3385.61, 3385.61, 3385.61, 3385.61, 3385.63, 3385.64, 3385.64, 3385.64, 3385.65, 3385.67, 3385.72, 3385.76, 3385.79, 3385.79, 3385.76, 3385.76, 3385.76, 3385.75, 3385.74, 3385.74, 3385.74, 3385.7, 3385.67, 3385.67, 3385.68, 3385.71, 3385.72, 3385.77, 3385.8, 3385.84, 3385.84, 3385.85, 3385.91, 3385.97, 3386.06, 3386.07, 3386.08, 3386.08, 3386.09, 3386.09, 3386.1, 3386.12, 3386.13, 3386.17, 3386.21, 3386.23, 3386.23, 3386.23, 3386.24, 3386.34, 3386.38, 3386.41, 3386.42], "senkou_b": [3384.81, 3384.81, 3384.81, 3384.81, 3384.81, 3384.81, 3384.81, 3384.81, 3384.81, 3384.81, 3384.83, 3384.86, 3384.93, 3384.93, 3384.93, 3384.93, 3384.93, 3384.93, 3384.93, 3384.93, 3384.93, 3384.93, 3384.93, 3384.93, 3384.93, 3385.0, 3385.0, 3385.01, 3385.01, 3385.01, 3385.05, 3385.07, 3385.15, 3385.16, 3385.16, 3385.16, 3385.16, 3385.16, 3385.16, 3385.16, 3385.16, 3385.16, 3385.16, 3385.16, 3385.16, 3385.16, 3385.16, 3385.16, 3385.16, 3385.16, 3385.16, 3385.16, 3385.16, 3385.19, 3385.23, 3385.27, 3385.27, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.28, 3385.29, 3385.29, 3385.29, 3385.29, 3385.29, 3385.33, 3385.33, 3385.33, 3385.33, 3385.33, 3385.33, 3385.36, 3385.37, 3385.37, 3385.37, 3385.39, 3385.44, 3385.44, 3385.45, 3385.45, 3385.45, 3385.45, 3385.45, 3385.45, 3385.45, 3385.5, 3385.55, 3385.56, 3385.56, 3385.56, 3385.56, 3385.56, 3385.56, 3385.56, 3385.56, 3385.56, 3385.59, 3385.59, 3385.59, 3385.59, 3385.59, 3385.59, 3385.59, 3385.59, 3385.59, 3385.59, 3385.59, 3385.59, 3385.63, 3385.7, 3385.75, 3385.81, 3385.85, 3385.85, 3385.85, 3385.86, 3385.93, 3385.98, 3386.04, 3386.07, 3386.12, 3386.12, 3386.13, 3386.16, 3386.16, 3386.17, 3386.17, 3386.17, 3386.17, 3386.17, 3386.17, 3386.17, 3386.17, 3386.17, 3386.17, 3386.17, 3386.17], "chikou": [3384.76, 3384.66, 3384.6, 3384.59, 3384.57, 3384.61, 3384.65, 3384.7, 3384.75, 3384.89, 3384.95, 3385.03, 3385.0, 3384.93, 3384.83, 3384.85, 3384.89, 3384.88, 3384.88, 3384.97, 3385.01, 3385.03, 3385.04, 3384.96, 3384.92, 3384.79, 3384.67, 3384.72, 3384.82, 3384.91, 3384.89, 3384.97, 3384.95, 3384.99, 3385.08, 3385.06, 3385.14, 3385.08, 3384.99, 3385.03, 3385.07, 3385.12, 3384.99, 3385.0, 3384.95, 3384.96, 3384.97, 3384.97, 3385.05, 3385.08, 3385.21, 3385.34, 3385.36, 3385.31, 3385.34, 3385.41, 3385.39, 3385.49, 3385.46, 3385.3, 3385.37, 3385.33, 3385.39, 3385.27, 3385.25, 3385.18, 3385.14, 3385.08, 3384.93, 3384.92, 3385.01, 3385.12, 3385.14, 3385.21, 3385.17, 3385.15, 3385.18, 3385.17, 3385.16, 3385.25, 3385.42, 3385.35, 3385.34, 3385.25, 3385.2, 3385.24, 3385.26, 3385.23, 3385.26, 3385.15, 3385.15, 3385.23, 3385.12, 3385.18, 3385.27, 3385.47, 3385.53, 3385.44, 3385.43, 3385.36, 3385.5, 3385.55, 3385.54, 3385.39, 3385.38, 3385.46, 3385.61, 3385.7, 3385.69, 3385.68, 3385.7, 3385.77, 3385.85, 3385.81, 3385.88, 3385.75, 3385.68, 3385.55, 3385.6, 3385.58, 3385.49, 3385.47, 3385.46, 3385.48, 3385.52, 3385.47, 3385.5, 3385.54, 3385.61, 3385.75, 3385.78, 3385.85, 3385.87, 3385.88, 3385.8, 3385.74, 3385.74, 3385.75, 3385.82, 3385.83, 3385.87, 3385.92, 3385.84, 3385.87, 3385.99, 3386.03, 3386.05, 3386.02, 3386.16, 3386.02, 3386.08, 3386.19, 3386.27, 3386.42, 3386.53, 3386.69, 3386.7, 3386.72, 3386.67, 3386.73, 3386.78, 3386.77, 3386.75, 3386.77, 3386.74, 3386.64, 3386.76, 3386.71, 3386.6, 3386.39, 3386.37, 3386.3, 3386.33], "current": {"tenkan": 3386.58, "kijun": 3386.4535, "senkou_a": 3386.42, "senkou_b": 3386.1735, "chikou": 3386.332}, "cloud_signal": "above_cloud"}, "overall_signal": {"signal": "bearish", "confidence": 66.67, "breakdown": {"bullish": 0, "bearish": 2, "neutral": 1}, "recommendation": "sell"}}}