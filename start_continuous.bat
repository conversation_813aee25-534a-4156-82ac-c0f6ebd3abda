@echo off
chcp 65001 >nul
title نظام التداول المستمر - PyQuotex

echo.
echo ========================================
echo 🚀 نظام التداول المستمر لـ PyQuotex
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo 📦 قم بتثبيت Python من: https://python.org
    pause
    exit /b 1
)

REM التحقق من وجود الملفات المطلوبة
if not exist "app.py" (
    echo ❌ ملف app.py غير موجود
    pause
    exit /b 1
)

if not exist "continuous_trading_system.py" (
    echo ❌ ملف continuous_trading_system.py غير موجود
    pause
    exit /b 1
)

echo ✅ جميع الملفات موجودة
echo.

REM إنشاء مجلدات البيانات
if not exist "data" mkdir data
if not exist "data\historical" mkdir data\historical
if not exist "data\realtime" mkdir data\realtime
if not exist "data\indicators" mkdir data\indicators

echo ✅ تم إنشاء مجلدات البيانات
echo.

echo 🔄 بدء النظام المستمر...
echo ⚠️ للإيقاف اضغط Ctrl+C
echo ========================================
echo.

REM تشغيل النظام
python app.py start-continuous

echo.
echo ========================================
echo ⏹️ تم إيقاف النظام
echo ========================================
pause
