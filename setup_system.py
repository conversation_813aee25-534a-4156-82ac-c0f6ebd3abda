#!/usr/bin/env python3
"""
إعداد النظام المستمر لـ PyQuotex
"""

import os
import sys
import json
import subprocess
from pathlib import Path

def print_banner():
    """طباعة شعار النظام"""
    print("🚀 إعداد نظام التداول المستمر لـ PyQuotex")
    print("=" * 60)
    print("📋 هذا المعالج سيساعدك في إعداد النظام للعمل بشكل مثالي")
    print("=" * 60)

def check_python_version():
    """التحقق من إصدار Python"""
    print("\n🐍 التحقق من إصدار Python...")
    
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        print(f"📊 الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("\n📦 تثبيت المتطلبات...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ ملف requirements.txt غير موجود")
        return False
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ تم تثبيت جميع المتطلبات")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت المتطلبات")
        return False

def setup_credentials():
    """إعداد بيانات تسجيل الدخول"""
    print("\n🔐 إعداد بيانات تسجيل الدخول...")
    
    config_file = Path("pyquotex/config.py")
    if not config_file.exists():
        print("❌ ملف الإعدادات غير موجود")
        return False
    
    print("📧 أدخل بيانات حساب Quotex:")
    email = input("البريد الإلكتروني: ").strip()
    password = input("كلمة المرور: ").strip()
    
    if not email or not password:
        print("❌ يجب إدخال البريد الإلكتروني وكلمة المرور")
        return False
    
    # قراءة ملف الإعدادات الحالي
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن دالة credentials وتحديثها
        if 'def credentials():' in content:
            # استبدال البيانات الافتراضية
            new_content = content.replace(
                'return "your_email", "your_password"',
                f'return "{email}", "{password}"'
            )
            
            # إذا لم يتم العثور على النمط الافتراضي، ابحث عن أنماط أخرى
            if new_content == content:
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if 'return' in line and 'credentials' in content[max(0, content.find(line) - 100):content.find(line)]:
                        lines[i] = f'    return "{email}", "{password}"'
                        break
                new_content = '\n'.join(lines)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ تم حفظ بيانات تسجيل الدخول")
            return True
        else:
            print("❌ لم يتم العثور على دالة credentials في ملف الإعدادات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إعداد بيانات تسجيل الدخول: {e}")
        return False

def create_data_directories():
    """إنشاء مجلدات البيانات"""
    print("\n📁 إنشاء مجلدات البيانات...")
    
    directories = [
        "data",
        "data/historical",
        "data/realtime", 
        "data/indicators"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ {directory}/")
    
    return True

def test_system():
    """اختبار النظام"""
    print("\n🧪 اختبار النظام...")
    
    try:
        # اختبار الاستيرادات
        from pyquotex.stable_api import Quotex
        from pyquotex.config import credentials
        from data_manager import DataManager
        from continuous_trading_system import ContinuousTradingSystem
        
        print("✅ جميع الوحدات متاحة")
        
        # اختبار بيانات تسجيل الدخول
        email, password = credentials()
        if email and password and email != "your_email":
            print("✅ بيانات تسجيل الدخول محفوظة")
        else:
            print("⚠️ بيانات تسجيل الدخول لم يتم إعدادها بشكل صحيح")
            return False
        
        # اختبار مدير البيانات
        dm = DataManager()
        print("✅ مدير البيانات يعمل")
        
        print("✅ جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ فشل في الاختبار: {e}")
        return False

def show_usage_instructions():
    """عرض تعليمات الاستخدام"""
    print("\n🎉 تم إعداد النظام بنجاح!")
    print("=" * 60)
    print("📋 تعليمات الاستخدام:")
    print()
    print("🚀 لبدء النظام المستمر:")
    print("   python app.py start-continuous")
    print("   أو")
    print("   python run_continuous.py")
    print()
    print("📊 لعرض حالة النظام:")
    print("   python app.py system-status")
    print()
    print("📈 لعرض بيانات أصل محدد:")
    print("   python app.py show-asset-data --asset EURUSD_otc")
    print()
    print("🧪 لاختبار النظام:")
    print("   python test_system.py")
    print()
    print("📚 للمزيد من المعلومات:")
    print("   اقرأ ملف README_CONTINUOUS_SYSTEM.md")
    print()
    print("⚠️ ملاحظات مهمة:")
    print("   • تأكد من اتصال الإنترنت")
    print("   • تأكد من صحة بيانات تسجيل الدخول")
    print("   • النظام يعمل 24/7 حتى تقوم بإيقافه (Ctrl+C)")
    print("   • جميع البيانات محفوظة في مجلد data/")
    print("=" * 60)

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # التحقق من إصدار Python
    if not check_python_version():
        sys.exit(1)
    
    # تثبيت المتطلبات
    print("\n❓ هل تريد تثبيت المتطلبات؟ (y/n): ", end="")
    if input().lower().startswith('y'):
        if not install_requirements():
            print("⚠️ يمكنك تثبيت المتطلبات يدوياً: pip install -r requirements.txt")
    
    # إعداد بيانات تسجيل الدخول
    print("\n❓ هل تريد إعداد بيانات تسجيل الدخول؟ (y/n): ", end="")
    if input().lower().startswith('y'):
        if not setup_credentials():
            print("⚠️ يمكنك إعداد بيانات تسجيل الدخول يدوياً في pyquotex/config.py")
    
    # إنشاء مجلدات البيانات
    create_data_directories()
    
    # اختبار النظام
    print("\n❓ هل تريد اختبار النظام؟ (y/n): ", end="")
    if input().lower().startswith('y'):
        if test_system():
            show_usage_instructions()
        else:
            print("⚠️ هناك مشاكل في النظام، راجع الأخطاء أعلاه")
    else:
        show_usage_instructions()

if __name__ == "__main__":
    main()
