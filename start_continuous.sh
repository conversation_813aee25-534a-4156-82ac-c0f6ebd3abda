#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

echo ""
echo "========================================"
echo "🚀 نظام التداول المستمر لـ PyQuotex"
echo "========================================"
echo ""

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python غير مثبت"
        echo "📦 قم بتثبيت Python من: https://python.org"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ تم العثور على Python: $($PYTHON_CMD --version)"

# التحقق من وجود الملفات المطلوبة
if [ ! -f "app.py" ]; then
    echo "❌ ملف app.py غير موجود"
    exit 1
fi

if [ ! -f "continuous_trading_system.py" ]; then
    echo "❌ ملف continuous_trading_system.py غير موجود"
    exit 1
fi

echo "✅ جميع الملفات موجودة"
echo ""

# إنشاء مجلدات البيانات
mkdir -p data/historical
mkdir -p data/realtime
mkdir -p data/indicators

echo "✅ تم إنشاء مجلدات البيانات"
echo ""

echo "🔄 بدء النظام المستمر..."
echo "⚠️ للإيقاف اضغط Ctrl+C"
echo "========================================"
echo ""

# تشغيل النظام
$PYTHON_CMD app.py start-continuous

echo ""
echo "========================================"
echo "⏹️ تم إيقاف النظام"
echo "========================================"
