{"asset": "USDJPY", "timeframe": 300, "timestamp": 1753230117.768234, "last_update": "2025-07-23T03:21:57.768234", "indicators": {"asset": "USDJPY", "timeframe": 300, "last_update": "2025-07-23T03:21:57.747923", "candles_count": 199, "timestamps": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "rsi": {"values": [33.25, 27.93, 25.26, 25.04, 23.79, 27.34, 40.6, 40.07, 39.06, 39.95, 35.33, 31.07, 29.54, 27.48, 32.7, 30.9, 35.06, 36.94, 39.47, 42.12, 46.01, 45.26, 49.98, 49.98, 49.52, 47.76, 49.34, 42.3, 40.28, 40.01, 45.41, 34.04, 26.45, 21.75, 29.48, 31.2, 26.65, 25.61, 24.64, 24.53, 33.74, 33.02, 32.27, 30.71, 29.32, 23.39, 18.83, 14.93, 14.69, 17.4, 23.33, 20.69, 20.34, 19.16, 22.99, 29.77, 25.92, 24.19, 22.17, 18.44, 22.97, 21.39, 35.79, 30.38, 30.19, 30.66, 31.66, 40.07, 39.84, 31.65, 30.39, 27.0, 35.59, 39.39, 39.7, 35.02, 39.49, 39.13, 41.33, 42.64, 36.56, 42.65, 43.57, 39.42, 37.47, 42.67, 37.41, 43.13, 45.62, 43.12, 41.58, 44.51, 46.24, 43.21, 44.23, 47.15, 46.76, 50.52, 50.52, 52.51, 53.34, 50.4, 55.32, 53.35, 54.89, 57.45, 57.06, 58.27, 57.69, 58.36, 58.95, 59.32, 54.22, 55.5, 57.62, 55.35, 57.88, 51.58, 53.5, 50.27, 55.71, 59.88, 58.3, 51.07, 44.52, 50.15, 52.6, 55.24, 57.27, 50.53, 54.6, 53.3, 51.48, 57.82, 59.34, 64.12, 62.01, 59.53, 61.08, 62.62, 63.27, 64.22, 56.81, 62.22, 56.06, 53.13, 53.29, 53.09, 53.28, 54.47, 53.73, 52.97, 54.14, 50.85, 50.04, 50.32, 50.93, 64.98, 57.95, 62.18, 63.87, 65.06, 60.2, 69.54, 59.66, 57.89, 45.66, 56.57, 55.59, 47.39, 54.6, 28.16, 45.85, 55.13, 58.2, 59.94, 61.87, 62.25, 63.23, 57.04, 51.96, 46.99, 51.0, 45.03, 38.22], "current": 38.22, "signal": "neutral"}, "macd": {"macd_line": [-0.08, -0.08, -0.09, -0.09, -0.09, -0.09, -0.08, -0.07, -0.07, -0.06, -0.06, -0.05, -0.04, -0.03, -0.03, -0.03, -0.03, -0.03, -0.03, -0.03, -0.03, -0.04, -0.06, -0.06, -0.07, -0.08, -0.09, -0.09, -0.09, -0.09, -0.09, -0.09, -0.09, -0.09, -0.1, -0.11, -0.13, -0.15, -0.16, -0.16, -0.16, -0.16, -0.16, -0.16, -0.15, -0.15, -0.15, -0.16, -0.17, -0.17, -0.17, -0.16, -0.16, -0.16, -0.16, -0.15, -0.14, -0.13, -0.13, -0.14, -0.15, -0.14, -0.13, -0.12, -0.12, -0.12, -0.12, -0.11, -0.1, -0.11, -0.1, -0.09, -0.09, -0.09, -0.08, -0.09, -0.08, -0.08, -0.07, -0.07, -0.06, -0.06, -0.06, -0.05, -0.05, -0.05, -0.04, -0.03, -0.02, -0.01, -0.01, 0.0, 0.01, 0.02, 0.02, 0.02, 0.02, 0.03, 0.03, 0.04, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.04, 0.04, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.03, 0.04, 0.04, 0.04, 0.04, 0.04, 0.03, 0.03, 0.03, 0.03, 0.03, 0.0, -0.01, 0.0, 0.02, 0.03, 0.04, 0.05, 0.06, 0.06, 0.05, 0.03, 0.03, 0.02, 0.0], "signal_line": [-0.08222222222222222, -0.08, -0.08, -0.07, -0.06, -0.05, -0.05, -0.05, -0.05, -0.05, -0.05, -0.05, -0.05, -0.05, -0.05, -0.05, -0.05, -0.06, -0.07, -0.07, -0.07, -0.07, -0.07, -0.07, -0.07, -0.07, -0.08, -0.09, -0.1, -0.11, -0.12, -0.13, -0.14, -0.14, -0.14, -0.14, -0.14, -0.14, -0.14, -0.14, -0.15, -0.15, -0.15, -0.15, -0.15, -0.15, -0.15, -0.15, -0.15, -0.15, -0.15, -0.15, -0.15, -0.15, -0.15, -0.14, -0.14, -0.14, -0.14, -0.13, -0.12, -0.12, -0.12, -0.11, -0.11, -0.11, -0.1, -0.1, -0.1, -0.1, -0.09, -0.09, -0.08, -0.08, -0.08, -0.07, -0.07, -0.07, -0.06, -0.05, -0.04, -0.03, -0.03, -0.02, -0.01, -0.0, 0.0, 0.0, 0.0, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.01, 0.01, 0.01, 0.02, 0.03, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.03], "histogram": [0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, -0.01, -0.01, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.03, -0.04, -0.04, -0.03, -0.02, -0.02, -0.02, -0.02, -0.01, -0.01, -0.01, -0.02, -0.02, -0.02, -0.02, -0.01, -0.01, -0.01, -0.01, 0.0, 0.01, 0.02, 0.02, 0.01, 0.0, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.02, 0.02, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.01, 0.01, 0.01, 0.01, -0.02, -0.02, -0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, -0.01, -0.01, -0.02, -0.03], "current": {"macd": 0.0, "signal": 0.03, "histogram": -0.03}, "trend": "bearish"}, "moving_averages": {"sma_20": {"values": [147.64, 147.63, 147.62, 147.61, 147.61, 147.6, 147.59, 147.58, 147.56, 147.55, 147.53, 147.52, 147.51, 147.5, 147.49, 147.49, 147.49, 147.49, 147.49, 147.49, 147.5, 147.49, 147.49, 147.49, 147.48, 147.48, 147.48, 147.47, 147.46, 147.45, 147.45, 147.43, 147.42, 147.4, 147.39, 147.37, 147.36, 147.34, 147.32, 147.31, 147.28, 147.25, 147.22, 147.19, 147.15, 147.12, 147.09, 147.07, 147.04, 147.02, 147.0, 146.97, 146.95, 146.92, 146.89, 146.86, 146.83, 146.8, 146.77, 146.74, 146.71, 146.69, 146.68, 146.68, 146.66, 146.64, 146.61, 146.6, 146.59, 146.57, 146.55, 146.54, 146.52, 146.52, 146.51, 146.51, 146.5, 146.49, 146.49, 146.48, 146.47, 146.46, 146.45, 146.44, 146.44, 146.43, 146.44, 146.43, 146.43, 146.42, 146.42, 146.42, 146.42, 146.42, 146.42, 146.43, 146.43, 146.43, 146.44, 146.44, 146.45, 146.46, 146.47, 146.47, 146.48, 146.49, 146.5, 146.5, 146.51, 146.52, 146.53, 146.53, 146.54, 146.54, 146.54, 146.55, 146.55, 146.55, 146.56, 146.55, 146.55, 146.55, 146.56, 146.56, 146.56, 146.56, 146.56, 146.56, 146.56, 146.56, 146.57, 146.57, 146.58, 146.58, 146.59, 146.59, 146.6, 146.6, 146.6, 146.61, 146.62, 146.62, 146.62, 146.62, 146.63, 146.63, 146.63, 146.64, 146.64, 146.64, 146.64, 146.64, 146.64, 146.64, 146.64, 146.64, 146.64, 146.65, 146.65, 146.65, 146.66, 146.66, 146.66, 146.66, 146.66, 146.67, 146.65, 146.65, 146.65, 146.66, 146.67, 146.69, 146.7, 146.71, 146.72, 146.72, 146.72, 146.73, 146.72, 146.71], "current": 146.71}, "ema_20": {"values": [147.6378, 147.63, 147.62, 147.61, 147.61, 147.6, 147.59, 147.58, 147.56, 147.55, 147.54, 147.53, 147.52, 147.52, 147.52, 147.52, 147.52, 147.52, 147.52, 147.52, 147.52, 147.52, 147.52, 147.52, 147.52, 147.52, 147.51, 147.49, 147.46, 147.44, 147.42, 147.4, 147.38, 147.36, 147.34, 147.33, 147.32, 147.31, 147.3, 147.29, 147.27, 147.24, 147.2, 147.16, 147.13, 147.1, 147.07, 147.04, 147.01, 146.99, 146.97, 146.95, 146.93, 146.9, 146.87, 146.84, 146.81, 146.8, 146.78, 146.76, 146.74, 146.72, 146.71, 146.7, 146.68, 146.66, 146.63, 146.61, 146.6, 146.59, 146.57, 146.56, 146.55, 146.54, 146.54, 146.53, 146.52, 146.52, 146.51, 146.5, 146.49, 146.48, 146.47, 146.47, 146.46, 146.45, 146.45, 146.45, 146.44, 146.44, 146.44, 146.44, 146.44, 146.44, 146.45, 146.45, 146.45, 146.46, 146.46, 146.47, 146.48, 146.49, 146.5, 146.51, 146.51, 146.52, 146.52, 146.52, 146.52, 146.52, 146.52, 146.52, 146.52, 146.52, 146.52, 146.52, 146.53, 146.54, 146.54, 146.54, 146.54, 146.54, 146.54, 146.54, 146.54, 146.54, 146.54, 146.54, 146.55, 146.56, 146.57, 146.58, 146.58, 146.59, 146.6, 146.61, 146.62, 146.62, 146.63, 146.63, 146.63, 146.63, 146.63, 146.63, 146.63, 146.63, 146.63, 146.63, 146.63, 146.63, 146.63, 146.63, 146.63, 146.63, 146.63, 146.64, 146.65, 146.65, 146.66, 146.66, 146.66, 146.66, 146.66, 146.66, 146.66, 146.66, 146.63, 146.62, 146.63, 146.65, 146.67, 146.69, 146.71, 146.73, 146.74, 146.74, 146.73, 146.73, 146.72, 146.7], "current": 146.7}, "sma_50": {"values": [147.53, 147.52, 147.51, 147.5, 147.49, 147.48, 147.47, 147.46, 147.45, 147.44, 147.43, 147.41, 147.39, 147.38, 147.36, 147.35, 147.33, 147.32, 147.3, 147.29, 147.28, 147.26, 147.24, 147.22, 147.2, 147.18, 147.17, 147.15, 147.13, 147.11, 147.1, 147.08, 147.06, 147.05, 147.03, 147.01, 146.98, 146.96, 146.94, 146.92, 146.9, 146.88, 146.86, 146.84, 146.82, 146.79, 146.78, 146.76, 146.74, 146.72, 146.71, 146.69, 146.68, 146.66, 146.65, 146.63, 146.61, 146.6, 146.58, 146.57, 146.55, 146.54, 146.54, 146.53, 146.52, 146.52, 146.51, 146.5, 146.5, 146.49, 146.49, 146.49, 146.48, 146.48, 146.48, 146.48, 146.48, 146.48, 146.48, 146.48, 146.48, 146.48, 146.48, 146.48, 146.48, 146.48, 146.48, 146.49, 146.49, 146.49, 146.49, 146.49, 146.49, 146.5, 146.5, 146.5, 146.5, 146.5, 146.51, 146.51, 146.52, 146.52, 146.53, 146.53, 146.54, 146.54, 146.55, 146.55, 146.56, 146.56, 146.57, 146.57, 146.57, 146.58, 146.58, 146.58, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.6, 146.6, 146.6, 146.6, 146.61, 146.61, 146.61, 146.62, 146.62, 146.62, 146.62, 146.63, 146.63, 146.63, 146.63, 146.63, 146.63, 146.64, 146.64, 146.65, 146.65, 146.66, 146.67, 146.67, 146.67, 146.68, 146.68, 146.67], "current": 146.67}, "ema_50": {"values": [147.53204, 147.52, 147.51, 147.5, 147.49, 147.48, 147.47, 147.46, 147.45, 147.44, 147.43, 147.41, 147.39, 147.37, 147.35, 147.33, 147.31, 147.29, 147.27, 147.25, 147.23, 147.21, 147.19, 147.17, 147.15, 147.13, 147.11, 147.09, 147.07, 147.05, 147.03, 147.01, 147.0, 146.99, 146.97, 146.95, 146.93, 146.91, 146.89, 146.87, 146.85, 146.83, 146.82, 146.81, 146.8, 146.78, 146.77, 146.76, 146.75, 146.74, 146.73, 146.71, 146.7, 146.69, 146.68, 146.67, 146.66, 146.65, 146.64, 146.63, 146.62, 146.61, 146.6, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.6, 146.6, 146.6, 146.6, 146.6, 146.6, 146.6, 146.6, 146.59, 146.59, 146.6, 146.61, 146.62, 146.63, 146.64, 146.65, 146.66, 146.66, 146.66, 146.66, 146.66, 146.65], "current": 146.65}, "trend": "bearish"}, "bollinger_bands": {"upper": [147.83, 147.81, 147.79, 147.78, 147.78, 147.78, 147.77, 147.77, 147.75, 147.73, 147.69, 147.68, 147.66, 147.63, 147.6, 147.59, 147.59, 147.59, 147.59, 147.59, 147.61, 147.59, 147.58, 147.58, 147.56, 147.56, 147.57, 147.58, 147.62, 147.63, 147.64, 147.65, 147.67, 147.67, 147.68, 147.66, 147.65, 147.63, 147.6, 147.58, 147.56, 147.54, 147.54, 147.54, 147.51, 147.46, 147.44, 147.43, 147.42, 147.4, 147.37, 147.34, 147.32, 147.3, 147.29, 147.25, 147.21, 147.15, 147.09, 147.02, 146.96, 146.93, 146.91, 146.9, 146.89, 146.87, 146.86, 146.84, 146.83, 146.79, 146.75, 146.72, 146.69, 146.67, 146.66, 146.67, 146.66, 146.63, 146.63, 146.63, 146.62, 146.62, 146.58, 146.54, 146.54, 146.53, 146.54, 146.53, 146.52, 146.51, 146.51, 146.5, 146.5, 146.5, 146.5, 146.52, 146.52, 146.52, 146.54, 146.54, 146.56, 146.57, 146.58, 146.59, 146.6, 146.61, 146.61, 146.61, 146.61, 146.61, 146.61, 146.6, 146.6, 146.59, 146.59, 146.6, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.6, 146.6, 146.6, 146.6, 146.6, 146.6, 146.61, 146.61, 146.64, 146.64, 146.66, 146.66, 146.67, 146.68, 146.69, 146.7, 146.7, 146.7, 146.7, 146.7, 146.69, 146.69, 146.69, 146.69, 146.68, 146.67, 146.67, 146.67, 146.67, 146.67, 146.68, 146.68, 146.68, 146.69, 146.69, 146.7, 146.72, 146.72, 146.73, 146.73, 146.73, 146.73, 146.73, 146.74, 146.81, 146.81, 146.82, 146.84, 146.87, 146.9, 146.93, 146.96, 146.98, 146.98, 146.98, 146.99, 146.98, 146.99], "middle": [147.64, 147.63, 147.62, 147.61, 147.61, 147.6, 147.59, 147.58, 147.56, 147.55, 147.53, 147.52, 147.51, 147.5, 147.49, 147.49, 147.49, 147.49, 147.49, 147.49, 147.5, 147.49, 147.49, 147.49, 147.48, 147.48, 147.48, 147.47, 147.46, 147.45, 147.45, 147.43, 147.42, 147.4, 147.39, 147.37, 147.36, 147.34, 147.32, 147.31, 147.28, 147.25, 147.22, 147.19, 147.15, 147.12, 147.09, 147.07, 147.04, 147.02, 147.0, 146.97, 146.95, 146.92, 146.89, 146.86, 146.83, 146.8, 146.77, 146.74, 146.71, 146.69, 146.68, 146.68, 146.66, 146.64, 146.61, 146.6, 146.59, 146.57, 146.55, 146.54, 146.52, 146.52, 146.51, 146.51, 146.5, 146.49, 146.49, 146.48, 146.47, 146.46, 146.45, 146.44, 146.44, 146.43, 146.44, 146.43, 146.43, 146.42, 146.42, 146.42, 146.42, 146.42, 146.42, 146.43, 146.43, 146.43, 146.44, 146.44, 146.45, 146.46, 146.47, 146.47, 146.48, 146.49, 146.5, 146.5, 146.51, 146.52, 146.53, 146.53, 146.54, 146.54, 146.54, 146.55, 146.55, 146.55, 146.56, 146.55, 146.55, 146.55, 146.56, 146.56, 146.56, 146.56, 146.56, 146.56, 146.56, 146.56, 146.57, 146.57, 146.58, 146.58, 146.59, 146.59, 146.6, 146.6, 146.6, 146.61, 146.62, 146.62, 146.62, 146.62, 146.63, 146.63, 146.63, 146.64, 146.64, 146.64, 146.64, 146.64, 146.64, 146.64, 146.64, 146.64, 146.64, 146.65, 146.65, 146.65, 146.66, 146.66, 146.66, 146.66, 146.66, 146.67, 146.65, 146.65, 146.65, 146.66, 146.67, 146.69, 146.7, 146.71, 146.72, 146.72, 146.72, 146.73, 146.72, 146.71], "lower": [147.45, 147.45, 147.45, 147.44, 147.44, 147.42, 147.41, 147.39, 147.37, 147.37, 147.37, 147.36, 147.36, 147.37, 147.38, 147.39, 147.39, 147.39, 147.39, 147.39, 147.39, 147.39, 147.4, 147.4, 147.4, 147.4, 147.39, 147.36, 147.3, 147.27, 147.26, 147.21, 147.17, 147.13, 147.1, 147.08, 147.07, 147.05, 147.04, 147.04, 147.0, 146.96, 146.9, 146.84, 146.79, 146.78, 146.74, 146.71, 146.66, 146.64, 146.63, 146.6, 146.58, 146.54, 146.49, 146.47, 146.45, 146.45, 146.45, 146.46, 146.46, 146.45, 146.45, 146.46, 146.43, 146.41, 146.36, 146.36, 146.35, 146.35, 146.35, 146.36, 146.35, 146.37, 146.36, 146.35, 146.34, 146.35, 146.35, 146.33, 146.32, 146.3, 146.32, 146.34, 146.34, 146.33, 146.34, 146.33, 146.34, 146.33, 146.33, 146.34, 146.34, 146.34, 146.34, 146.34, 146.34, 146.34, 146.34, 146.34, 146.34, 146.35, 146.36, 146.35, 146.36, 146.37, 146.39, 146.39, 146.41, 146.43, 146.45, 146.46, 146.48, 146.49, 146.49, 146.5, 146.51, 146.51, 146.53, 146.51, 146.51, 146.51, 146.52, 146.52, 146.52, 146.52, 146.52, 146.52, 146.51, 146.51, 146.5, 146.5, 146.5, 146.5, 146.51, 146.5, 146.51, 146.5, 146.5, 146.52, 146.54, 146.54, 146.55, 146.55, 146.57, 146.57, 146.58, 146.61, 146.61, 146.61, 146.61, 146.61, 146.6, 146.6, 146.6, 146.59, 146.59, 146.6, 146.58, 146.58, 146.59, 146.59, 146.59, 146.59, 146.59, 146.6, 146.49, 146.49, 146.48, 146.48, 146.47, 146.48, 146.47, 146.46, 146.46, 146.46, 146.46, 146.47, 146.46, 146.43], "current": {"upper": 146.99, "middle": 146.71, "lower": 146.43}, "position": "within_bands"}, "stochastic": {"k_values": [3.55, 2.42, 15.69, 9.0, 9.48, 2.29, 12.81, 40.62, 38.75, 35.99, 39.67, 28.99, 5.88, 5.86, 3.67, 30.28, 21.94, 32.91, 37.97, 44.73, 51.9, 62.87, 60.34, 79.19, 79.19, 87.76, 77.78, 82.13, 59.9, 42.69, 35.9, 45.11, 0.6, 0.38, 4.07, 17.89, 21.14, 10.65, 12.58, 8.31, 20.12, 33.26, 31.14, 29.03, 24.58, 22.2, 0.29, 15.44, 10.86, 10.99, 18.92, 27.94, 14.89, 13.05, 9.46, 14.29, 25.24, 9.83, 4.9, 4.33, 5.61, 14.94, 6.17, 42.67, 9.77, 8.57, 11.55, 26.44, 50.26, 49.21, 5.76, 10.67, 2.7, 36.75, 51.0, 52.14, 24.22, 40.69, 38.68, 46.42, 51.0, 19.48, 41.26, 49.06, 39.15, 22.12, 52.4, 12.0, 45.33, 63.72, 43.72, 31.16, 48.37, 58.6, 37.91, 43.6, 59.72, 67.6, 92.13, 88.17, 89.86, 87.8, 69.54, 99.43, 79.49, 84.42, 97.86, 82.19, 87.02, 85.1, 85.05, 83.73, 82.22, 60.74, 67.41, 78.52, 69.63, 75.26, 32.56, 45.24, 8.57, 57.14, 90.91, 78.48, 27.85, 0.98, 42.48, 57.52, 74.34, 87.61, 51.33, 76.99, 69.91, 60.18, 100.0, 92.59, 97.65, 90.64, 83.04, 90.06, 96.06, 96.09, 87.68, 63.77, 91.3, 68.84, 55.3, 56.06, 29.76, 22.67, 30.67, 26.67, 17.14, 24.29, 7.14, 4.23, 7.14, 10.0, 87.21, 59.3, 86.05, 80.0, 86.67, 71.43, 78.61, 55.49, 50.87, 12.14, 54.91, 51.45, 24.73, 54.84, 18.06, 59.59, 89.98, 100.0, 95.27, 96.44, 97.05, 97.15, 84.88, 73.47, 60.91, 70.9, 54.64, 31.67], "d_values": [7.22, 9.04, 11.39, 6.92, 8.19, 18.57, 30.73, 38.45, 38.14, 34.88, 24.85, 13.58, 5.14, 13.27, 18.63, 28.38, 30.94, 38.54, 44.87, 53.17, 58.37, 67.47, 72.91, 82.05, 81.58, 82.56, 73.27, 61.57, 46.16, 41.23, 27.2, 15.36, 1.68, 7.45, 14.37, 16.56, 14.79, 10.51, 13.67, 20.56, 28.17, 31.14, 28.25, 25.27, 15.69, 12.64, 8.86, 12.43, 13.59, 19.28, 20.58, 18.63, 12.47, 12.27, 16.33, 16.45, 13.32, 6.35, 4.95, 8.29, 8.91, 21.26, 19.54, 20.34, 9.96, 15.52, 29.42, 41.97, 35.08, 21.88, 6.38, 16.71, 30.15, 46.63, 42.45, 39.02, 34.53, 41.93, 45.37, 38.97, 37.25, 36.6, 43.16, 36.78, 37.89, 28.84, 36.58, 40.35, 50.92, 46.2, 41.08, 46.04, 48.29, 46.7, 47.08, 56.97, 73.15, 82.63, 90.05, 88.61, 82.4, 85.59, 82.82, 87.78, 87.26, 88.16, 89.02, 84.77, 85.72, 84.63, 83.67, 75.56, 70.12, 68.89, 71.85, 74.47, 59.15, 51.02, 28.79, 36.98, 52.21, 75.51, 65.75, 35.77, 23.77, 33.66, 58.11, 73.16, 71.09, 71.98, 66.08, 69.03, 76.7, 84.26, 96.75, 93.63, 90.44, 87.91, 89.72, 94.07, 93.28, 82.51, 80.92, 74.64, 71.81, 60.07, 47.04, 36.16, 27.7, 26.67, 24.83, 22.7, 16.19, 11.89, 6.17, 7.12, 34.78, 52.17, 77.52, 75.12, 84.24, 79.37, 78.9, 68.51, 61.66, 39.5, 39.31, 39.5, 43.7, 43.67, 32.54, 44.16, 55.88, 83.19, 95.08, 97.24, 96.25, 96.88, 93.03, 85.17, 73.09, 68.43, 62.15, 52.4], "current": {"k": 31.67, "d": 52.4}, "signal": "neutral"}, "atr": {"values": [0.055928571428568476, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.08, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.09, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.09, 0.09, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.1, 0.11, 0.12, 0.12, 0.12, 0.12, 0.11, 0.11, 0.11, 0.11, 0.11, 0.11, 0.11, 0.12], "current": 0.12, "volatility": "high"}, "adx": {"adx": [33.17644176485518, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, 33.18, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN], "plus_di": [16.99, 26130.02, 39466328.64, 60061376746.76, 93934695328794.64, 1.4931396647550262e+17, 2.2771317619783533e+20, 3.484572266322463e+23, 5.452145376578295e+26, 8.565215895403306e+29, 1.289642222294253e+33, 1.9298703806143214e+36, 2.88478478190257e+39, 4.362966680786031e+42, 6.480933686607995e+45, 9.823615626224865e+48, 1.550993608386198e+52, 2.5407705759621283e+55, 4.318264881682365e+58, 7.604063100011504e+61, 1.3421546496648357e+65, 2.4102307793993106e+68, 4.4056131946366093e+71, 8.429861002385293e+74, 1.6607369428352914e+78, 3.213965134117608e+81, 6.420411505302953e+84, 1.2776385720864545e+88, 2.654873618541691e+91, 5.63089264982369e+94, 1.1488946926697477e+98, 2.168866928786665e+101, 3.81301022461669e+104, 6.25374395872106e+107, 1.0136804496502502e+111, 1.6115194184195688e+114, 2.3477876565929082e+117, 3.348162399815691e+120, 4.7493280630225154e+123, 6.465008090261953e+126, 8.675247655393962e+129, 1.1384924830385283e+133, 1.54519175770769e+136, 2.0893586121215874e+139, 2.859886018433549e+142, 3.688684331867492e+145, 4.3165267659794077e+148, 4.505179720490751e+151, 4.580604168867063e+154, 4.590257887813509e+157, 4.5705548279111086e+160, 4.500122063940502e+163, 4.3787426013066766e+166, 4.281245291796486e+169, 4.259511086138935e+172, 4.25198076985213e+175, 4.244534147042371e+178, 4.283212637805462e+181, 4.359531083308073e+184, 4.2262088953189955e+187, 4.193230701130112e+190, 4.213857256540997e+193, 4.060092280258394e+196, 3.7437188992265034e+199, 3.562430059694449e+202, 3.4675440147493895e+205, 3.2708521422826703e+208, 3.0773705420611767e+211, 2.9633086927060117e+214, 2.709820694158062e+217, 2.427712867072637e+220, 2.1580900120318632e+223, 1.8854809394101038e+226, 1.668935794196412e+229, 1.528146302470885e+232, 1.3584841574135862e+235, 1.2307649609540188e+238, 1.1615957446169645e+241, 1.1362279226612964e+244, 1.1520967699938325e+247, 1.1024721158464848e+250, 1.0565690915296627e+253, 1.0361743016018156e+256, 1.028340888472054e+259, 1.0392624989841951e+262, 1.0651060756600727e+265, 1.0619586581451625e+268, 1.0605549977299464e+271, 1.0807225026885116e+274, 1.1136027324263133e+277, 1.182264312938447e+280, 1.2818156135434709e+283, 1.4335626741344052e+286, 1.6046932927742055e+289, 1.8503507970524344e+292, 2.171225619864333e+295, 2.6189803777401157e+298, 3.218748869588837e+301, 4.1176316733460075e+304, 5.414525181262042e+307, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity], "minus_di": [33.84, 52076.95, 78656117.76, 119701905014.82, 187211192731273.3, 2.9758169393612166e+17, 4.53830772191321e+20, 6.944728226915039e+23, 1.0866087714670373e+27, 1.707041554951185e+30, 2.5702479556380446e+33, 3.8462182105019616e+36, 5.749355953118801e+39, 8.695362169406832e+42, 1.2916455642244962e+46, 1.9578397437516137e+49, 3.091119445570331e+52, 5.063738039682404e+55, 8.606271795523801e+58, 1.5154844730950431e+62, 2.6749048571891764e+65, 4.803573135459507e+68, 8.780356374113112e+71, 1.6800654191632104e+75, 3.3098371458259027e+78, 6.405410099525206e+81, 1.2795835356958941e+85, 2.5463247644820502e+88, 5.291144607839326e+91, 1.122232978374403e+95, 2.28973911060742e+98, 4.322536664353235e+101, 7.599302787414398e+104, 1.2463668098886975e+108, 2.0202580671938826e+111, 3.211746962886614e+114, 4.6791244271570664e+117, 6.672864314229117e+120, 9.46537770989077e+123, 1.2884716039785372e+127, 1.7289708079860413e+130, 2.2690075793528097e+133, 3.0795563976276057e+136, 4.1640771436955674e+139, 5.699732891158009e+142, 7.351522150159028e+145, 8.60280774304866e+148, 8.978791765807511e+151, 9.129112165444762e+154, 9.148351959984265e+157, 9.109083898999204e+160, 8.968711891572726e+163, 8.726803468990193e+166, 8.532492011039494e+169, 8.489175890728732e+172, 8.474168022882226e+175, 8.45932695555183e+178, 8.536412917915982e+181, 8.688515047591606e+184, 8.42280485665934e+187, 8.357079545616233e+190, 8.398188126709653e+193, 8.091735601267353e+196, 7.461205659118809e+199, 7.099898266159601e+202, 6.910791040277403e+205, 6.518785510093345e+208, 6.133178030107005e+211, 5.905853559759043e+214, 5.400653746366155e+217, 4.838414814280102e+220, 4.3010583444143656e+223, 3.7577503637342864e+226, 3.326177399413455e+229, 3.045584924208119e+232, 2.707449452257673e+235, 2.4529059843709365e+238, 2.3150522185667062e+241, 2.2644943263130286e+244, 2.2961208283844508e+247, 2.1972192387291737e+250, 2.1057348313731343e+253, 2.0650881572711788e+256, 2.049476219530322e+259, 2.071242913118538e+262, 2.12274898121189e+265, 2.1164762000533715e+268, 2.1136787146343527e+271, 2.153872411377515e+274, 2.219402479952776e+277, 2.356244531097968e+280, 2.554649578977046e+283, 2.857080412517901e+286, 3.1981425420777862e+289, 3.6877362349975254e+292, 4.327237519222561e+295, 5.219609629225374e+298, 6.414944050959714e+301, 8.206411210436693e+304, 1.0791110927751691e+308, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity], "current": {"adx": NaN, "plus_di": Infinity, "minus_di": Infinity}, "trend_strength": "very_strong"}, "ichimoku": {"tenkan": [147.72, 147.72, 147.69, 147.69, 147.69, 147.69, 147.69, 147.63, 147.62, 147.61, 147.6, 147.56, 147.56, 147.56, 147.54, 147.53, 147.52, 147.52, 147.51, 147.5, 147.48, 147.48, 147.48, 147.47, 147.47, 147.44, 147.44, 147.45, 147.45, 147.47, 147.49, 147.5, 147.51, 147.51, 147.52, 147.51, 147.51, 147.49, 147.44, 147.38, 147.37, 147.37, 147.34, 147.33, 147.33, 147.28, 147.23, 147.18, 147.18, 147.18, 147.18, 147.16, 147.07, 147.01, 147.0, 146.99, 146.96, 146.96, 146.95, 146.94, 146.88, 146.83, 146.8, 146.79, 146.76, 146.7, 146.67, 146.67, 146.67, 146.67, 146.67, 146.63, 146.58, 146.56, 146.56, 146.56, 146.54, 146.51, 146.5, 146.5, 146.5, 146.5, 146.5, 146.48, 146.43, 146.43, 146.43, 146.44, 146.44, 146.43, 146.43, 146.43, 146.41, 146.41, 146.41, 146.41, 146.41, 146.4, 146.39, 146.39, 146.39, 146.4, 146.41, 146.42, 146.42, 146.44, 146.45, 146.45, 146.45, 146.46, 146.48, 146.49, 146.52, 146.52, 146.52, 146.53, 146.53, 146.55, 146.55, 146.55, 146.56, 146.56, 146.56, 146.56, 146.56, 146.55, 146.55, 146.56, 146.56, 146.56, 146.55, 146.55, 146.55, 146.55, 146.55, 146.55, 146.55, 146.54, 146.55, 146.55, 146.58, 146.6, 146.6, 146.6, 146.6, 146.6, 146.61, 146.62, 146.64, 146.65, 146.65, 146.65, 146.65, 146.65, 146.65, 146.65, 146.65, 146.65, 146.65, 146.63, 146.63, 146.63, 146.63, 146.65, 146.65, 146.65, 146.66, 146.66, 146.66, 146.69, 146.69, 146.69, 146.7, 146.7, 146.7, 146.69, 146.69, 146.51, 146.49, 146.52, 146.52, 146.56, 146.58, 146.58, 146.59, 146.59, 146.63, 146.75, 146.79, 146.78, 146.69], "kijun": [147.63, 147.62, 147.58, 147.56, 147.56, 147.56, 147.56, 147.56, 147.56, 147.56, 147.55, 147.52, 147.52, 147.52, 147.5, 147.49, 147.48, 147.48, 147.48, 147.48, 147.48, 147.45, 147.4, 147.39, 147.39, 147.36, 147.35, 147.35, 147.32, 147.32, 147.32, 147.32, 147.32, 147.32, 147.31, 147.23, 147.16, 147.16, 147.14, 147.14, 147.13, 147.12, 147.12, 147.12, 147.12, 147.1, 147.04, 146.97, 146.9, 146.9, 146.9, 146.88, 146.88, 146.88, 146.88, 146.85, 146.83, 146.83, 146.82, 146.8, 146.7, 146.65, 146.61, 146.61, 146.61, 146.61, 146.58, 146.58, 146.58, 146.58, 146.58, 146.54, 146.52, 146.5, 146.5, 146.49, 146.49, 146.49, 146.49, 146.49, 146.49, 146.49, 146.49, 146.47, 146.42, 146.42, 146.42, 146.42, 146.42, 146.42, 146.42, 146.42, 146.43, 146.43, 146.43, 146.45, 146.45, 146.45, 146.45, 146.45, 146.45, 146.46, 146.47, 146.47, 146.47, 146.47, 146.48, 146.48, 146.49, 146.49, 146.5, 146.52, 146.53, 146.53, 146.53, 146.53, 146.53, 146.55, 146.55, 146.55, 146.55, 146.55, 146.55, 146.56, 146.57, 146.58, 146.58, 146.58, 146.58, 146.58, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.61, 146.61, 146.62, 146.62, 146.62, 146.62, 146.62, 146.62, 146.65, 146.66, 146.66, 146.66, 146.69, 146.69, 146.69, 146.69, 146.69, 146.69, 146.69, 146.69, 146.51, 146.51, 146.52, 146.52, 146.56, 146.58, 146.58, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59], "senkou_a": [147.67, 147.67, 147.64, 147.63, 147.63, 147.63, 147.62, 147.6, 147.59, 147.58, 147.58, 147.54, 147.54, 147.54, 147.52, 147.51, 147.5, 147.5, 147.5, 147.49, 147.48, 147.47, 147.44, 147.43, 147.43, 147.4, 147.39, 147.4, 147.38, 147.39, 147.4, 147.41, 147.41, 147.41, 147.42, 147.37, 147.34, 147.32, 147.29, 147.26, 147.25, 147.25, 147.23, 147.22, 147.22, 147.19, 147.14, 147.08, 147.04, 147.04, 147.04, 147.02, 146.98, 146.94, 146.94, 146.92, 146.9, 146.9, 146.89, 146.87, 146.79, 146.74, 146.7, 146.7, 146.69, 146.65, 146.62, 146.62, 146.62, 146.62, 146.62, 146.59, 146.55, 146.53, 146.53, 146.53, 146.52, 146.5, 146.49, 146.49, 146.49, 146.49, 146.49, 146.48, 146.43, 146.42, 146.42, 146.43, 146.43, 146.42, 146.42, 146.42, 146.42, 146.42, 146.42, 146.43, 146.43, 146.42, 146.42, 146.42, 146.42, 146.43, 146.44, 146.45, 146.45, 146.45, 146.46, 146.46, 146.47, 146.47, 146.49, 146.5, 146.53, 146.53, 146.53, 146.53, 146.53, 146.55, 146.55, 146.55, 146.55, 146.55, 146.55, 146.56, 146.57, 146.56, 146.56, 146.57, 146.57, 146.57, 146.57, 146.57, 146.57, 146.57, 146.57, 146.57, 146.57, 146.56, 146.57, 146.57, 146.59, 146.61, 146.61, 146.61, 146.61, 146.61, 146.62, 146.62, 146.64, 146.65, 146.65, 146.65, 146.67, 146.67, 146.67, 146.67, 146.67, 146.67, 146.67, 146.66, 146.57, 146.57, 146.57, 146.59, 146.6, 146.61, 146.62, 146.63, 146.63, 146.64, 146.64, 146.64, 146.65, 146.65], "senkou_b": [147.47, 147.47, 147.42, 147.41, 147.41, 147.41, 147.41, 147.41, 147.41, 147.32, 147.25, 147.21, 147.2, 147.2, 147.18, 147.16, 147.15, 147.15, 147.15, 147.15, 147.14, 147.12, 147.05, 147.05, 147.04, 147.04, 147.04, 147.04, 147.04, 147.01, 147.01, 147.01, 147.01, 146.99, 146.96, 146.95, 146.95, 146.95, 146.95, 146.95, 146.93, 146.93, 146.93, 146.93, 146.93, 146.91, 146.86, 146.81, 146.81, 146.8, 146.8, 146.78, 146.78, 146.78, 146.78, 146.78, 146.76, 146.76, 146.75, 146.75, 146.68, 146.64, 146.6, 146.6, 146.6, 146.6, 146.57, 146.57, 146.57, 146.57, 146.57, 146.53, 146.51, 146.49, 146.49, 146.49, 146.49, 146.49, 146.49, 146.49, 146.49, 146.49, 146.49, 146.47, 146.45, 146.45, 146.45, 146.45, 146.45, 146.45, 146.45, 146.45, 146.45, 146.45, 146.45, 146.45, 146.45, 146.47, 146.48, 146.48, 146.48, 146.49, 146.51, 146.51, 146.52, 146.52, 146.53, 146.53, 146.53, 146.53, 146.54, 146.56, 146.57, 146.57, 146.57, 146.57, 146.57, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.6, 146.6, 146.6, 146.63, 146.63, 146.63, 146.63, 146.63, 146.63, 146.63, 146.63, 146.51, 146.51, 146.52, 146.52, 146.56, 146.58, 146.58, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59, 146.59], "chikou": [147.42, 147.4, 147.36, 147.4, 147.41, 147.44, 147.45, 147.46, 147.46, 147.5, 147.5, 147.53, 147.53, 147.51, 147.52, 147.48, 147.47, 147.45, 147.47, 147.4, 147.3, 147.2, 147.21, 147.21, 147.16, 147.12, 147.15, 147.06, 147.14, 147.17, 147.18, 147.14, 147.13, 147.06, 146.88, 146.75, 146.74, 146.71, 146.79, 146.78, 146.72, 146.7, 146.74, 146.74, 146.74, 146.68, 146.63, 146.51, 146.52, 146.5, 146.53, 146.51, 146.5, 146.5, 146.45, 146.54, 146.61, 146.47, 146.41, 146.34, 146.32, 146.42, 146.48, 146.36, 146.39, 146.42, 146.45, 146.47, 146.34, 146.38, 146.45, 146.41, 146.36, 146.37, 146.31, 146.32, 146.38, 146.4, 146.35, 146.36, 146.39, 146.37, 146.38, 146.38, 146.4, 146.43, 146.46, 146.47, 146.47, 146.47, 146.46, 146.5, 146.51, 146.51, 146.55, 146.53, 146.54, 146.54, 146.56, 146.54, 146.53, 146.54, 146.54, 146.55, 146.54, 146.52, 146.53, 146.53, 146.53, 146.56, 146.57, 146.54, 146.5, 146.49, 146.53, 146.54, 146.57, 146.55, 146.55, 146.55, 146.56, 146.55, 146.6, 146.62, 146.63, 146.61, 146.63, 146.63, 146.65, 146.66, 146.63, 146.63, 146.64, 146.63, 146.62, 146.62, 146.62, 146.63, 146.62, 146.63, 146.63, 146.62, 146.61, 146.61, 146.61, 146.61, 146.65, 146.65, 146.68, 146.68, 146.68, 146.68, 146.7, 146.69, 146.63, 146.62, 146.69, 146.59, 146.63, 146.24, 146.31, 146.56, 146.65, 146.75, 146.83, 146.88, 146.87, 146.82, 146.75, 146.63, 146.66, 146.62, 146.44], "current": {"tenkan": 146.69150000000002, "kijun": 146.5925, "senkou_a": 146.64600000000002, "senkou_b": 146.5925, "chikou": 146.44}, "cloud_signal": "below_cloud"}, "overall_signal": {"signal": "bearish", "confidence": 66.67, "breakdown": {"bullish": 0, "bearish": 2, "neutral": 1}, "recommendation": "sell"}}}