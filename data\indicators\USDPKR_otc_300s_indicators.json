{"asset": "USDPKR_otc", "timeframe": 300, "timestamp": 1753230124.4788873, "last_update": "2025-07-23T03:22:04.478887", "indicators": {"asset": "USDPKR_otc", "timeframe": 300, "last_update": "2025-07-23T03:22:04.449732", "candles_count": 199, "timestamps": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "rsi": {"values": [58.89, 63.96, 59.75, 58.11, 63.18, 59.71, 63.7, 66.85, 66.08, 68.86, 67.61, 71.45, 72.52, 65.16, 65.44, 67.78, 68.82, 71.51, 69.62, 71.7, 75.3, 76.46, 62.62, 63.22, 60.99, 63.59, 63.92, 60.44, 54.7, 57.05, 49.06, 50.4, 54.68, 60.3, 64.71, 65.64, 69.82, 73.61, 75.35, 76.46, 69.17, 62.96, 64.41, 63.52, 66.31, 61.38, 57.39, 65.21, 61.54, 54.33, 53.46, 56.45, 47.34, 46.3, 55.16, 59.73, 62.76, 63.68, 62.74, 57.18, 55.26, 50.86, 51.98, 51.49, 54.93, 56.33, 48.94, 43.64, 37.25, 34.61, 33.45, 31.15, 31.4, 30.28, 31.66, 29.41, 26.99, 24.19, 32.79, 30.99, 36.85, 37.58, 35.59, 35.21, 32.64, 30.91, 35.07, 33.22, 41.76, 41.03, 52.16, 51.84, 56.61, 58.72, 60.2, 52.88, 59.42, 57.58, 48.34, 52.04, 54.19, 48.56, 46.31, 48.39, 47.36, 55.69, 51.32, 49.37, 52.25, 57.39, 53.43, 54.39, 48.62, 47.79, 53.07, 55.96, 52.98, 55.78, 49.81, 51.63, 47.42, 46.57, 39.78, 34.98, 41.58, 39.16, 41.32, 46.17, 42.85, 37.58, 36.6, 32.35, 33.07, 35.87, 39.44, 37.71, 32.73, 29.1, 30.63, 30.05, 29.5, 29.96, 30.12, 29.82, 25.84, 26.75, 25.35, 24.05, 34.8, 34.45, 32.35, 33.98, 30.87, 33.85, 46.94, 49.71, 47.41, 49.91, 41.99, 42.41, 39.42, 40.36, 43.34, 33.92, 40.2, 42.22, 39.36, 51.79, 49.72, 43.86, 49.36, 50.44, 55.56, 53.71, 46.22, 47.65, 52.42, 56.03, 60.23, 48.32, 47.68, 43.63, 46.1, 44.8, 42.09], "current": 42.09, "signal": "neutral"}, "macd": {"macd_line": [0.12, 0.12, 0.12, 0.12, 0.12, 0.11, 0.11, 0.11, 0.11, 0.11, 0.11, 0.11, 0.11, 0.1, 0.1, 0.1, 0.09, 0.08, 0.07, 0.06, 0.05, 0.05, 0.05, 0.06, 0.07, 0.07, 0.08, 0.09, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.09, 0.08, 0.08, 0.08, 0.07, 0.06, 0.05, 0.04, 0.03, 0.03, 0.03, 0.04, 0.05, 0.06, 0.05, 0.05, 0.04, 0.04, 0.04, 0.04, 0.04, 0.03, 0.02, 0.0, -0.02, -0.04, -0.05, -0.05, -0.05, -0.06, -0.07, -0.07, -0.08, -0.09, -0.09, -0.09, -0.08, -0.08, -0.08, -0.08, -0.08, -0.08, -0.08, -0.07, -0.06, -0.05, -0.04, -0.03, -0.02, -0.01, 0.0, 0.01, 0.01, 0.01, 0.01, 0.02, 0.01, 0.0, 0.0, 0.0, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.02, 0.01, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.0, -0.01, -0.02, -0.02, -0.03, -0.03, -0.03, -0.03, -0.04, -0.05, -0.05, -0.06, -0.06, -0.06, -0.06, -0.07, -0.08, -0.08, -0.09, -0.1, -0.1, -0.1, -0.1, -0.1, -0.1, -0.11, -0.11, -0.1, -0.09, -0.09, -0.08, -0.08, -0.08, -0.07, -0.06, -0.05, -0.04, -0.03, -0.03, -0.03, -0.03, -0.03, -0.04, -0.04, -0.04, -0.04, -0.03, -0.02, -0.03, -0.03, -0.02, -0.01, 0.0, -0.01, -0.01, 0.0, 0.0, 0.01, 0.0, 0.0, 0.0, -0.01, -0.02, -0.02], "signal_line": [0.11555555555555556, 0.11, 0.11, 0.11, 0.11, 0.11, 0.11, 0.11, 0.11, 0.1, 0.09, 0.08, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.08, 0.07, 0.06, 0.05, 0.05, 0.05, 0.05, 0.05, 0.05, 0.05, 0.05, 0.05, 0.05, 0.05, 0.05, 0.05, 0.05, 0.04, 0.03, 0.02, 0.01, -0.0, -0.01, -0.02, -0.03, -0.04, -0.05, -0.06, -0.07, -0.07, -0.07, -0.07, -0.07, -0.07, -0.07, -0.07, -0.07, -0.07, -0.07, -0.07, -0.07, -0.06, -0.05, -0.04, -0.03, -0.02, -0.01, -0.01, -0.01, -0.01, -0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.0, -0.0, -0.0, -0.01, -0.01, -0.01, -0.01, -0.02, -0.03, -0.03, -0.04, -0.04, -0.04, -0.04, -0.05, -0.06, -0.06, -0.07, -0.08, -0.08, -0.08, -0.08, -0.08, -0.08, -0.09, -0.09, -0.09, -0.09, -0.09, -0.09, -0.09, -0.09, -0.09, -0.08, -0.07, -0.06, -0.05, -0.05, -0.05, -0.05, -0.05, -0.05, -0.05, -0.05, -0.05, -0.05, -0.04, -0.04, -0.04, -0.04, -0.03, -0.02, -0.02, -0.02, -0.02, -0.02, -0.01, -0.01, -0.01, -0.01, -0.01, -0.01, -0.01], "histogram": [-0.01, 0.0, 0.0, 0.0, 0.0, -0.01, -0.01, -0.01, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.01, 0.0, 0.0, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.0, 0.0, 0.0, -0.01, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.01, 0.0, 0.01, 0.0, 0.0, -0.01, -0.01, -0.01, -0.01, -0.01, -0.02, -0.02, -0.03, -0.04, -0.05, -0.05, -0.04, -0.03, -0.03, -0.03, -0.02, -0.02, -0.02, -0.02, -0.02, -0.01, -0.01, -0.01, -0.01, -0.01, -0.01, -0.01, 0.0, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.0, 0.0, 0.0, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.02, 0.01, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.0, -0.01, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.02, -0.01, 0.0, 0.0, 0.01, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.01, 0.01, 0.01, 0.02, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.01, 0.01, 0.01, 0.0, -0.01, -0.01], "current": {"macd": -0.02, "signal": -0.01, "histogram": -0.01}, "trend": "bearish"}, "moving_averages": {"sma_20": {"values": [294.03, 294.04, 294.06, 294.07, 294.09, 294.1, 294.12, 294.14, 294.16, 294.18, 294.2, 294.22, 294.23, 294.25, 294.27, 294.29, 294.31, 294.32, 294.34, 294.35, 294.36, 294.37, 294.38, 294.39, 294.39, 294.39, 294.39, 294.39, 294.4, 294.41, 294.42, 294.43, 294.44, 294.45, 294.47, 294.48, 294.48, 294.49, 294.5, 294.51, 294.52, 294.53, 294.54, 294.56, 294.57, 294.58, 294.59, 294.6, 294.6, 294.61, 294.62, 294.62, 294.63, 294.63, 294.63, 294.63, 294.63, 294.63, 294.63, 294.63, 294.64, 294.64, 294.63, 294.62, 294.61, 294.6, 294.59, 294.58, 294.57, 294.56, 294.54, 294.51, 294.49, 294.46, 294.44, 294.42, 294.41, 294.39, 294.37, 294.35, 294.32, 294.31, 294.29, 294.28, 294.27, 294.27, 294.27, 294.27, 294.28, 294.28, 294.29, 294.3, 294.31, 294.31, 294.32, 294.33, 294.33, 294.34, 294.34, 294.35, 294.36, 294.37, 294.37, 294.38, 294.39, 294.39, 294.4, 294.39, 294.39, 294.39, 294.39, 294.39, 294.39, 294.39, 294.39, 294.39, 294.39, 294.39, 294.38, 294.38, 294.37, 294.36, 294.36, 294.35, 294.34, 294.33, 294.31, 294.3, 294.29, 294.28, 294.26, 294.24, 294.22, 294.2, 294.18, 294.16, 294.14, 294.13, 294.12, 294.1, 294.09, 294.07, 294.05, 294.04, 294.02, 294.01, 294.0, 293.99, 293.98, 293.98, 293.97, 293.97, 293.97, 293.97, 293.96, 293.96, 293.96, 293.96, 293.95, 293.95, 293.95, 293.94, 293.95, 293.95, 293.95, 293.95, 293.95, 293.96, 293.96, 293.96, 293.96, 293.95, 293.96, 293.96, 293.96, 293.96, 293.96, 293.96, 293.96, 293.96], "current": 293.96}, "ema_20": {"values": [294.0331, 294.05, 294.07, 294.09, 294.11, 294.13, 294.15, 294.17, 294.18, 294.19, 294.21, 294.22, 294.24, 294.25, 294.27, 294.29, 294.31, 294.32, 294.33, 294.34, 294.35, 294.36, 294.37, 294.37, 294.37, 294.37, 294.37, 294.37, 294.38, 294.39, 294.4, 294.42, 294.44, 294.46, 294.48, 294.5, 294.51, 294.52, 294.53, 294.54, 294.55, 294.55, 294.56, 294.57, 294.57, 294.57, 294.57, 294.56, 294.55, 294.56, 294.57, 294.58, 294.6, 294.61, 294.62, 294.62, 294.62, 294.62, 294.62, 294.62, 294.63, 294.63, 294.62, 294.6, 294.58, 294.56, 294.54, 294.52, 294.5, 294.48, 294.46, 294.44, 294.42, 294.41, 294.39, 294.38, 294.37, 294.36, 294.35, 294.34, 294.33, 294.32, 294.31, 294.31, 294.3, 294.31, 294.31, 294.32, 294.33, 294.34, 294.34, 294.35, 294.36, 294.36, 294.36, 294.37, 294.37, 294.37, 294.37, 294.37, 294.38, 294.38, 294.38, 294.38, 294.39, 294.39, 294.39, 294.39, 294.39, 294.39, 294.4, 294.4, 294.4, 294.4, 294.4, 294.4, 294.4, 294.39, 294.37, 294.36, 294.35, 294.34, 294.34, 294.33, 294.32, 294.31, 294.29, 294.27, 294.26, 294.25, 294.24, 294.22, 294.2, 294.18, 294.16, 294.14, 294.13, 294.12, 294.11, 294.09, 294.08, 294.06, 294.04, 294.03, 294.02, 294.01, 294.0, 293.99, 293.98, 293.98, 293.98, 293.98, 293.98, 293.98, 293.98, 293.98, 293.98, 293.98, 293.97, 293.96, 293.96, 293.95, 293.95, 293.95, 293.95, 293.95, 293.95, 293.96, 293.97, 293.97, 293.97, 293.97, 293.98, 293.99, 293.99, 293.99, 293.98, 293.98, 293.97, 293.96], "current": 293.96}, "sma_50": {"values": [294.24, 294.25, 294.26, 294.28, 294.29, 294.31, 294.32, 294.33, 294.35, 294.36, 294.37, 294.38, 294.4, 294.41, 294.42, 294.43, 294.44, 294.45, 294.45, 294.46, 294.47, 294.48, 294.49, 294.5, 294.51, 294.52, 294.52, 294.53, 294.53, 294.54, 294.55, 294.55, 294.56, 294.56, 294.56, 294.55, 294.55, 294.55, 294.55, 294.55, 294.54, 294.54, 294.54, 294.53, 294.53, 294.53, 294.53, 294.53, 294.52, 294.52, 294.51, 294.5, 294.5, 294.49, 294.48, 294.47, 294.47, 294.46, 294.46, 294.45, 294.45, 294.45, 294.44, 294.44, 294.43, 294.43, 294.42, 294.42, 294.42, 294.41, 294.41, 294.4, 294.39, 294.39, 294.38, 294.38, 294.37, 294.37, 294.36, 294.36, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.34, 294.34, 294.34, 294.34, 294.34, 294.34, 294.34, 294.34, 294.34, 294.33, 294.33, 294.33, 294.33, 294.32, 294.32, 294.32, 294.31, 294.3, 294.3, 294.29, 294.28, 294.27, 294.26, 294.25, 294.24, 294.23, 294.22, 294.21, 294.2, 294.2, 294.19, 294.18, 294.17, 294.17, 294.16, 294.15, 294.14, 294.13, 294.12, 294.11, 294.1, 294.09, 294.08, 294.07, 294.06, 294.05, 294.04, 294.04, 294.03, 294.02, 294.02, 294.01, 294.01, 294.0, 294.0, 293.99, 293.99, 293.98, 293.98, 293.98, 293.97, 293.97], "current": 293.97}, "ema_50": {"values": [294.23928, 294.25, 294.27, 294.29, 294.31, 294.32, 294.33, 294.34, 294.35, 294.36, 294.37, 294.38, 294.39, 294.4, 294.41, 294.42, 294.43, 294.43, 294.43, 294.44, 294.45, 294.46, 294.47, 294.48, 294.49, 294.5, 294.5, 294.5, 294.5, 294.51, 294.52, 294.52, 294.52, 294.52, 294.52, 294.51, 294.5, 294.49, 294.48, 294.47, 294.46, 294.45, 294.44, 294.43, 294.42, 294.41, 294.41, 294.4, 294.39, 294.38, 294.37, 294.36, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.35, 294.34, 294.34, 294.34, 294.34, 294.34, 294.34, 294.33, 294.32, 294.31, 294.3, 294.29, 294.28, 294.27, 294.26, 294.25, 294.24, 294.23, 294.22, 294.21, 294.2, 294.19, 294.18, 294.17, 294.16, 294.15, 294.14, 294.13, 294.12, 294.11, 294.1, 294.09, 294.09, 294.09, 294.09, 294.09, 294.08, 294.08, 294.07, 294.06, 294.06, 294.05, 294.04, 294.04, 294.03, 294.03, 294.03, 294.03, 294.03, 294.03, 294.03, 294.03, 294.03, 294.03, 294.03, 294.03, 294.03, 294.03, 294.03, 294.03, 294.03, 294.03, 294.02], "current": 294.02}, "trend": "bearish"}, "bollinger_bands": {"upper": [294.2, 294.22, 294.26, 294.28, 294.32, 294.34, 294.37, 294.39, 294.4, 294.41, 294.42, 294.43, 294.45, 294.45, 294.47, 294.49, 294.53, 294.52, 294.53, 294.52, 294.51, 294.51, 294.51, 294.51, 294.5, 294.49, 294.49, 294.49, 294.5, 294.51, 294.52, 294.55, 294.59, 294.63, 294.68, 294.7, 294.71, 294.73, 294.74, 294.75, 294.76, 294.77, 294.79, 294.8, 294.79, 294.78, 294.75, 294.73, 294.72, 294.72, 294.72, 294.73, 294.75, 294.76, 294.76, 294.76, 294.76, 294.76, 294.76, 294.76, 294.77, 294.77, 294.76, 294.77, 294.79, 294.8, 294.82, 294.83, 294.85, 294.85, 294.85, 294.83, 294.82, 294.78, 294.76, 294.73, 294.71, 294.68, 294.65, 294.6, 294.53, 294.48, 294.43, 294.4, 294.37, 294.37, 294.37, 294.38, 294.41, 294.42, 294.44, 294.47, 294.48, 294.48, 294.49, 294.5, 294.5, 294.51, 294.5, 294.51, 294.5, 294.5, 294.48, 294.47, 294.47, 294.47, 294.48, 294.47, 294.47, 294.47, 294.47, 294.47, 294.47, 294.46, 294.46, 294.46, 294.46, 294.48, 294.5, 294.5, 294.51, 294.51, 294.51, 294.5, 294.5, 294.5, 294.5, 294.51, 294.51, 294.5, 294.47, 294.46, 294.44, 294.42, 294.4, 294.37, 294.35, 294.34, 294.33, 294.31, 294.31, 294.28, 294.25, 294.22, 294.19, 294.16, 294.15, 294.14, 294.12, 294.09, 294.06, 294.05, 294.05, 294.05, 294.04, 294.04, 294.04, 294.03, 294.03, 294.04, 294.04, 294.03, 294.04, 294.04, 294.04, 294.04, 294.04, 294.06, 294.06, 294.06, 294.05, 294.04, 294.05, 294.07, 294.07, 294.07, 294.07, 294.07, 294.06, 294.07], "middle": [294.03, 294.04, 294.06, 294.07, 294.09, 294.1, 294.12, 294.14, 294.16, 294.18, 294.2, 294.22, 294.23, 294.25, 294.27, 294.29, 294.31, 294.32, 294.34, 294.35, 294.36, 294.37, 294.38, 294.39, 294.39, 294.39, 294.39, 294.39, 294.4, 294.41, 294.42, 294.43, 294.44, 294.45, 294.47, 294.48, 294.48, 294.49, 294.5, 294.51, 294.52, 294.53, 294.54, 294.56, 294.57, 294.58, 294.59, 294.6, 294.6, 294.61, 294.62, 294.62, 294.63, 294.63, 294.63, 294.63, 294.63, 294.63, 294.63, 294.63, 294.64, 294.64, 294.63, 294.62, 294.61, 294.6, 294.59, 294.58, 294.57, 294.56, 294.54, 294.51, 294.49, 294.46, 294.44, 294.42, 294.41, 294.39, 294.37, 294.35, 294.32, 294.31, 294.29, 294.28, 294.27, 294.27, 294.27, 294.27, 294.28, 294.28, 294.29, 294.3, 294.31, 294.31, 294.32, 294.33, 294.33, 294.34, 294.34, 294.35, 294.36, 294.37, 294.37, 294.38, 294.39, 294.39, 294.4, 294.39, 294.39, 294.39, 294.39, 294.39, 294.39, 294.39, 294.39, 294.39, 294.39, 294.39, 294.38, 294.38, 294.37, 294.36, 294.36, 294.35, 294.34, 294.33, 294.31, 294.3, 294.29, 294.28, 294.26, 294.24, 294.22, 294.2, 294.18, 294.16, 294.14, 294.13, 294.12, 294.1, 294.09, 294.07, 294.05, 294.04, 294.02, 294.01, 294.0, 293.99, 293.98, 293.98, 293.97, 293.97, 293.97, 293.97, 293.96, 293.96, 293.96, 293.96, 293.95, 293.95, 293.95, 293.94, 293.95, 293.95, 293.95, 293.95, 293.95, 293.96, 293.96, 293.96, 293.96, 293.95, 293.96, 293.96, 293.96, 293.96, 293.96, 293.96, 293.96, 293.96], "lower": [293.86, 293.86, 293.86, 293.86, 293.86, 293.86, 293.87, 293.89, 293.92, 293.95, 293.98, 294.01, 294.01, 294.05, 294.07, 294.09, 294.09, 294.12, 294.15, 294.18, 294.21, 294.23, 294.25, 294.27, 294.28, 294.29, 294.29, 294.29, 294.3, 294.31, 294.32, 294.31, 294.29, 294.27, 294.26, 294.26, 294.25, 294.25, 294.26, 294.27, 294.28, 294.29, 294.29, 294.32, 294.35, 294.38, 294.43, 294.47, 294.48, 294.5, 294.52, 294.51, 294.51, 294.5, 294.5, 294.5, 294.5, 294.5, 294.5, 294.5, 294.51, 294.51, 294.5, 294.47, 294.43, 294.4, 294.36, 294.33, 294.29, 294.27, 294.23, 294.19, 294.16, 294.14, 294.12, 294.11, 294.11, 294.1, 294.09, 294.1, 294.11, 294.14, 294.15, 294.16, 294.17, 294.17, 294.17, 294.16, 294.15, 294.14, 294.14, 294.13, 294.14, 294.14, 294.15, 294.16, 294.16, 294.17, 294.18, 294.19, 294.22, 294.24, 294.26, 294.29, 294.31, 294.31, 294.32, 294.31, 294.31, 294.31, 294.31, 294.31, 294.31, 294.32, 294.32, 294.32, 294.32, 294.3, 294.26, 294.26, 294.23, 294.21, 294.21, 294.2, 294.18, 294.16, 294.12, 294.09, 294.07, 294.06, 294.05, 294.02, 294.0, 293.98, 293.96, 293.95, 293.93, 293.92, 293.91, 293.89, 293.87, 293.86, 293.85, 293.86, 293.85, 293.86, 293.85, 293.84, 293.84, 293.87, 293.88, 293.89, 293.89, 293.89, 293.88, 293.88, 293.88, 293.89, 293.87, 293.86, 293.86, 293.85, 293.86, 293.86, 293.86, 293.86, 293.86, 293.86, 293.86, 293.86, 293.87, 293.86, 293.87, 293.85, 293.85, 293.85, 293.85, 293.85, 293.86, 293.85], "current": {"upper": 294.07, "middle": 293.96, "lower": 293.85}, "position": "within_bands"}, "stochastic": {"k_values": [92.89, 92.63, 98.16, 78.91, 73.79, 96.21, 85.77, 96.72, 94.23, 92.35, 98.75, 82.57, 96.23, 95.77, 77.7, 77.62, 89.16, 94.41, 90.03, 86.07, 95.36, 100.0, 95.24, 59.56, 57.85, 50.83, 63.14, 64.83, 54.66, 32.43, 29.44, 13.0, 19.11, 36.89, 64.0, 89.33, 83.59, 91.12, 99.14, 96.71, 95.98, 80.82, 69.41, 74.2, 72.6, 81.51, 71.73, 57.3, 84.72, 66.81, 34.8, 13.26, 33.15, 11.6, 17.61, 58.52, 84.07, 97.54, 98.33, 88.51, 71.43, 65.22, 50.31, 54.35, 52.8, 64.6, 69.57, 47.2, 15.02, 3.0, 1.87, 5.61, 3.82, 4.53, 10.41, 13.66, 6.92, 0.0, 0.82, 13.99, 7.82, 19.19, 26.45, 24.33, 27.31, 18.18, 7.89, 24.16, 10.67, 52.94, 49.32, 100.0, 71.91, 93.19, 97.98, 98.11, 70.82, 96.14, 85.71, 54.7, 68.46, 76.85, 56.71, 35.95, 42.74, 14.71, 67.65, 41.18, 28.82, 47.06, 82.94, 60.0, 66.86, 31.95, 26.63, 59.17, 78.7, 62.13, 80.36, 45.83, 57.14, 31.55, 26.19, 1.93, 2.86, 25.19, 12.59, 20.0, 37.41, 22.22, 6.89, 1.98, 4.38, 16.75, 24.27, 33.7, 31.13, 8.68, 0.3, 12.47, 9.7, 11.38, 12.17, 15.06, 16.48, 14.05, 15.72, 8.7, 4.56, 30.33, 31.99, 31.5, 43.11, 22.16, 34.64, 90.2, 93.02, 81.98, 91.43, 43.9, 45.37, 31.22, 34.15, 41.12, 0.0, 24.78, 31.42, 18.14, 65.04, 56.19, 28.76, 52.21, 57.08, 100.0, 79.15, 42.65, 49.29, 72.51, 91.94, 94.37, 39.39, 32.42, 10.71, 22.32, 15.18, 3.45], "d_values": [94.56, 89.9, 83.62, 82.97, 85.26, 92.9, 92.24, 94.43, 95.11, 91.22, 92.52, 91.52, 89.9, 83.7, 81.49, 87.06, 91.2, 90.17, 90.49, 93.81, 96.87, 84.93, 70.88, 56.08, 57.27, 59.6, 60.88, 50.64, 38.84, 24.96, 20.52, 23.0, 40.0, 63.41, 78.97, 88.01, 91.28, 95.66, 97.28, 91.17, 82.07, 74.81, 72.07, 76.1, 75.28, 70.18, 71.25, 69.61, 62.11, 38.29, 27.07, 19.34, 20.79, 29.24, 53.4, 80.04, 93.31, 94.79, 86.09, 75.05, 62.32, 56.63, 52.49, 57.25, 62.32, 60.46, 43.93, 21.74, 6.63, 3.49, 3.77, 4.65, 6.25, 9.53, 10.33, 6.86, 2.58, 4.94, 7.54, 13.67, 17.82, 23.32, 26.03, 23.27, 17.79, 16.74, 14.24, 29.26, 37.64, 67.42, 73.74, 88.37, 87.69, 96.43, 88.97, 88.36, 84.22, 78.85, 69.62, 66.67, 67.34, 56.5, 45.13, 31.13, 41.7, 41.18, 45.88, 39.02, 52.94, 63.33, 69.93, 52.94, 41.81, 39.25, 54.83, 66.67, 73.73, 62.77, 61.11, 44.84, 38.29, 19.89, 10.33, 9.99, 13.55, 19.26, 23.33, 26.54, 22.17, 10.36, 4.42, 7.7, 15.13, 24.91, 29.7, 24.5, 13.37, 7.15, 7.49, 11.18, 11.08, 12.87, 14.57, 15.2, 15.42, 12.82, 9.66, 14.53, 22.29, 31.27, 35.53, 32.26, 33.3, 49.0, 72.62, 88.4, 88.81, 72.44, 60.23, 40.16, 36.91, 35.5, 25.09, 21.97, 18.73, 24.78, 38.2, 46.46, 50.0, 45.72, 46.02, 69.76, 78.74, 73.93, 57.03, 54.82, 71.25, 86.27, 75.23, 55.39, 27.51, 21.82, 16.07, 13.65], "current": {"k": 3.45, "d": 13.65}, "signal": "oversold"}, "atr": {"values": [0.05992857142857133, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07, 0.07], "current": 0.07, "volatility": "high"}, "adx": {"adx": [22.14017844674164, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 22.14, 20.56, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN, NaN], "plus_di": [27.29, 41698.28, 62938570.42, 96627245751.05, 144505517283376.28, 2.1198381136606954e+17, 3.045450307464829e+20, 4.3494361291708465e+23, 6.315961687221281e+26, 9.337940426402496e+29, 1.3834256492553774e+33, 2.0098818927492792e+36, 2.9728908702409844e+39, 4.3224498370219845e+42, 6.361958113508434e+45, 9.27026411972178e+48, 1.3925218923398285e+52, 2.032613179968943e+55, 2.965650473642703e+58, 4.230330834096025e+61, 5.98244719369255e+64, 8.53135807578542e+67, 1.1523050359187798e+71, 1.5641013137300817e+74, 2.1063667530076923e+77, 2.872193867257697e+80, 3.958385732365424e+83, 5.6061705339292e+86, 8.103786949093922e+89, 1.1665327476088694e+93, 1.620894146667612e+96, 2.306029112925322e+99, 3.218313104339732e+102, 4.3719847735900113e+105, 5.953860972685074e+108, 8.221659623676444e+111, 1.1050915305909795e+115, 1.4628179046931889e+118, 1.9666801242974947e+121, 2.7001324482955076e+124, 3.632816873574002e+127, 4.8877574067249805e+130, 6.772584641056936e+133, 9.495724939721573e+136, 1.3174938027645014e+140, 1.8435650330820225e+143, 2.5723846772139127e+146, 3.398027831483455e+149, 4.503751877207644e+152, 5.832857871964414e+155, 7.752678205081235e+158, 1.0551017019507392e+162, 1.339504664424032e+165, 1.7024668058690413e+168, 2.078552634652611e+171, 2.4933576304130616e+174, 3.025586955782917e+177, 3.8010069089087854e+180, 4.917307136231183e+183, 6.30981623813233e+186, 8.164069040272207e+189, 1.0477374958606098e+193, 1.3517622905197838e+196, 1.7422304190880303e+199, 2.217015592641625e+202, 2.840571038871152e+205, 3.6021352915841747e+208, 4.443163015412136e+211, 5.335171518154467e+214, 6.48613126278301e+217, 8.061649518353959e+220, 1.0007524776609082e+224, 1.2774419464082414e+227, 1.6318240029338616e+230, 2.1515171812283734e+233, 2.852421258692318e+236, 3.7779617295475007e+239, 4.711524698563265e+242, 5.789459364416731e+245, 7.17209655943764e+248, 8.809016570626729e+251, 1.1044961469332552e+255, 1.4025093900314622e+258, 1.8202729257023306e+261, 2.3520161054659254e+264, 3.109764476120921e+267, 4.1420982157236497e+270, 5.613260053620262e+273, 7.501530653085852e+276, 1.034931920708722e+280, 1.3729007065614e+283, 1.8249585111411098e+286, 2.408273308479456e+289, 3.2484892663306505e+292, 4.464486602537607e+295, 5.9811622449227785e+298, 7.6754149304951865e+301, 1.0143571978648213e+305, 1.2897505912297135e+308, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity], "minus_di": [17.4, 26580.81, 40120553.64, 61595593455.16, 92115872965544.9, 1.3513029955981662e+17, 1.9413398112348897e+20, 2.7725730718002385e+23, 4.026146097206862e+26, 5.952523822267037e+29, 8.818726354522713e+32, 1.2812107702792878e+36, 1.8950863807263782e+39, 2.755370504686341e+42, 4.055466783644236e+45, 5.909383171403492e+48, 8.876710879140408e+51, 1.2957009600328304e+55, 1.89047094827919e+58, 2.696648716544509e+61, 3.81354536544508e+64, 5.438363264646305e+67, 7.345434714309196e+70, 9.970453767399278e+73, 1.342715599283383e+77, 1.8308964971205529e+80, 2.523295747636795e+83, 3.5736856449148965e+86, 5.165805591205973e+89, 7.436130080636603e+92, 1.0332482946808947e+96, 1.4699915187633737e+99, 2.0515321951434683e+102, 2.7869468348503234e+105, 3.7953229144799345e+108, 5.240944205440753e+111, 7.044469509603205e+114, 9.324816852231272e+117, 1.25367155454964e+121, 1.7212149561707284e+124, 2.3157600064293707e+127, 3.11572906577232e+130, 4.3172230249216237e+133, 6.053104467615887e+136, 8.398440007681904e+139, 1.1751911316858793e+143, 1.639781404886265e+146, 2.1660923814036597e+149, 2.870942532772898e+152, 3.71818879210201e+155, 4.941989303298155e+158, 6.725806472290687e+161, 8.538749511056466e+164, 1.085247255369194e+168, 1.3249853295939515e+171, 1.5894051594611584e+174, 1.9286778034818395e+177, 2.422973711623669e+180, 3.1345657107705996e+183, 4.0222286453459522e+186, 5.204232756877354e+189, 6.678862917093401e+192, 8.616886453473774e+195, 1.1105947992747228e+199, 1.4132493383898072e+202, 1.8107383433197292e+205, 2.296201855556734e+208, 2.832319814407501e+211, 3.4009357639401847e+214, 4.134621671702099e+217, 5.138944843671163e+220, 6.379354216724536e+223, 8.143127146172783e+226, 1.0402155944095225e+230, 1.3714969993884085e+233, 1.8182923340889514e+236, 2.408283429519428e+239, 3.0033885125354248e+242, 3.6905241639043423e+245, 4.571894194670878e+248, 5.615358268847089e+251, 7.040674202239746e+254, 8.940376757502842e+257, 1.1603434438963536e+261, 1.499306191604763e+264, 1.9823372478807994e+267, 2.6404043265848627e+270, 3.578204900009971e+273, 4.781893852064645e+276, 6.597233041908165e+279, 8.751634502087304e+282, 1.1633302972785764e+286, 1.5351676691705564e+289, 2.0707681631314053e+292, 2.845912657641075e+295, 3.812726267462746e+298, 4.892737384613877e+301, 6.466078287998424e+304, 8.221589310390998e+307, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity, Infinity], "current": {"adx": NaN, "plus_di": Infinity, "minus_di": Infinity}, "trend_strength": "very_strong"}, "ichimoku": {"tenkan": [293.97, 293.97, 293.97, 293.98, 293.99, 294.0, 294.01, 294.04, 294.05, 294.06, 294.08, 294.1, 294.12, 294.15, 294.18, 294.19, 294.22, 294.23, 294.24, 294.25, 294.26, 294.29, 294.3, 294.34, 294.36, 294.36, 294.38, 294.4, 294.4, 294.41, 294.43, 294.43, 294.43, 294.43, 294.44, 294.43, 294.41, 294.38, 294.38, 294.38, 294.4, 294.42, 294.45, 294.47, 294.49, 294.51, 294.52, 294.56, 294.58, 294.62, 294.62, 294.65, 294.66, 294.65, 294.65, 294.65, 294.64, 294.64, 294.6, 294.58, 294.58, 294.58, 294.59, 294.6, 294.61, 294.61, 294.61, 294.61, 294.63, 294.68, 294.67, 294.67, 294.67, 294.64, 294.58, 294.54, 294.53, 294.51, 294.5, 294.48, 294.48, 294.46, 294.4, 294.33, 294.31, 294.3, 294.28, 294.28, 294.28, 294.27, 294.26, 294.26, 294.25, 294.25, 294.25, 294.24, 294.27, 294.3, 294.3, 294.31, 294.32, 294.32, 294.33, 294.36, 294.37, 294.4, 294.4, 294.4, 294.4, 294.4, 294.4, 294.4, 294.38, 294.37, 294.37, 294.39, 294.4, 294.4, 294.4, 294.4, 294.4, 294.4, 294.4, 294.4, 294.4, 294.39, 294.39, 294.39, 294.37, 294.34, 294.34, 294.34, 294.33, 294.33, 294.31, 294.28, 294.26, 294.21, 294.18, 294.18, 294.18, 294.18, 294.18, 294.13, 294.09, 294.08, 294.07, 294.07, 294.07, 294.06, 294.02, 293.98, 293.96, 293.96, 293.94, 293.94, 293.94, 293.94, 293.93, 293.93, 293.94, 293.95, 293.95, 293.96, 293.97, 293.97, 293.97, 293.97, 293.99, 293.97, 293.96, 293.96, 293.96, 293.92, 293.93, 293.93, 293.93, 293.93, 293.94, 293.97, 293.97, 293.97, 293.97, 293.97, 294.0, 294.01, 294.01, 294.0, 294.0, 294.0, 293.99], "kijun": [294.13, 294.14, 294.14, 294.14, 294.14, 294.14, 294.17, 294.17, 294.17, 294.2, 294.25, 294.27, 294.27, 294.27, 294.3, 294.31, 294.31, 294.31, 294.31, 294.32, 294.33, 294.36, 294.37, 294.38, 294.41, 294.44, 294.46, 294.49, 294.5, 294.51, 294.51, 294.51, 294.51, 294.51, 294.51, 294.51, 294.51, 294.51, 294.51, 294.51, 294.51, 294.51, 294.51, 294.51, 294.51, 294.51, 294.53, 294.57, 294.6, 294.61, 294.61, 294.61, 294.61, 294.61, 294.61, 294.61, 294.61, 294.6, 294.58, 294.57, 294.55, 294.55, 294.53, 294.53, 294.52, 294.51, 294.49, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.46, 294.45, 294.44, 294.44, 294.44, 294.43, 294.43, 294.43, 294.41, 294.36, 294.32, 294.32, 294.32, 294.33, 294.33, 294.33, 294.33, 294.33, 294.33, 294.33, 294.33, 294.33, 294.33, 294.33, 294.33, 294.33, 294.33, 294.33, 294.33, 294.33, 294.36, 294.37, 294.4, 294.4, 294.4, 294.4, 294.4, 294.4, 294.38, 294.34, 294.34, 294.34, 294.34, 294.34, 294.34, 294.32, 294.32, 294.28, 294.26, 294.26, 294.26, 294.26, 294.26, 294.24, 294.22, 294.22, 294.21, 294.21, 294.21, 294.2, 294.17, 294.16, 294.15, 294.12, 294.1, 294.1, 294.1, 294.1, 294.1, 294.1, 294.06, 294.04, 294.03, 294.03, 294.03, 294.03, 294.02, 294.0, 293.97, 293.97, 293.96, 293.96, 293.96, 293.96, 293.96, 293.96, 293.96, 293.96, 293.96, 293.96, 293.96, 293.96, 293.96, 293.96, 293.98, 293.98, 293.98, 293.98, 293.98, 293.98, 293.98], "senkou_a": [294.05, 294.06, 294.05, 294.06, 294.06, 294.07, 294.09, 294.11, 294.11, 294.13, 294.17, 294.18, 294.19, 294.21, 294.24, 294.25, 294.26, 294.27, 294.28, 294.28, 294.29, 294.33, 294.34, 294.36, 294.39, 294.4, 294.42, 294.44, 294.45, 294.46, 294.47, 294.47, 294.47, 294.47, 294.48, 294.47, 294.46, 294.45, 294.45, 294.45, 294.46, 294.47, 294.48, 294.49, 294.5, 294.51, 294.53, 294.56, 294.59, 294.61, 294.61, 294.63, 294.63, 294.63, 294.63, 294.63, 294.62, 294.62, 294.59, 294.58, 294.57, 294.57, 294.56, 294.56, 294.57, 294.56, 294.55, 294.55, 294.56, 294.58, 294.58, 294.58, 294.58, 294.55, 294.52, 294.49, 294.48, 294.47, 294.47, 294.46, 294.46, 294.43, 294.38, 294.33, 294.31, 294.31, 294.31, 294.31, 294.31, 294.3, 294.29, 294.29, 294.29, 294.29, 294.29, 294.29, 294.3, 294.32, 294.32, 294.32, 294.33, 294.33, 294.33, 294.36, 294.37, 294.4, 294.4, 294.4, 294.4, 294.4, 294.4, 294.39, 294.36, 294.36, 294.36, 294.36, 294.37, 294.37, 294.36, 294.36, 294.34, 294.33, 294.33, 294.33, 294.33, 294.32, 294.31, 294.31, 294.3, 294.27, 294.27, 294.27, 294.26, 294.25, 294.23, 294.22, 294.19, 294.15, 294.14, 294.14, 294.14, 294.14, 294.14, 294.1, 294.06, 294.05, 294.05, 294.05, 294.05, 294.04, 294.01, 293.98, 293.97, 293.96, 293.95, 293.95, 293.95, 293.95, 293.95, 293.94, 293.95, 293.96, 293.96, 293.96, 293.97, 293.97, 293.97, 293.98, 293.98, 293.97, 293.97, 293.97, 293.97, 293.95], "senkou_b": [294.27, 294.29, 294.31, 294.32, 294.32, 294.32, 294.32, 294.32, 294.32, 294.33, 294.35, 294.37, 294.37, 294.38, 294.4, 294.41, 294.41, 294.41, 294.42, 294.42, 294.44, 294.49, 294.5, 294.51, 294.52, 294.52, 294.53, 294.53, 294.53, 294.53, 294.53, 294.53, 294.53, 294.53, 294.53, 294.53, 294.53, 294.53, 294.52, 294.51, 294.49, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.48, 294.46, 294.45, 294.44, 294.44, 294.44, 294.43, 294.43, 294.43, 294.41, 294.36, 294.33, 294.33, 294.33, 294.33, 294.33, 294.33, 294.33, 294.33, 294.33, 294.32, 294.32, 294.28, 294.26, 294.26, 294.26, 294.26, 294.26, 294.24, 294.23, 294.23, 294.22, 294.22, 294.22, 294.22, 294.19, 294.19, 294.19, 294.18, 294.18, 294.18, 294.18, 294.18, 294.18, 294.18, 294.18, 294.18, 294.18, 294.18, 294.18, 294.18, 294.18, 294.18, 294.17, 294.17, 294.16, 294.16, 294.16, 294.16, 294.16, 294.15, 294.15, 294.13, 294.12, 294.1, 294.09, 294.09, 294.09, 294.09, 294.09, 294.09, 294.05, 294.03, 294.02, 294.02, 294.02], "chikou": [294.33, 294.29, 294.28, 294.3, 294.34, 294.35, 294.34, 294.35, 294.41, 294.46, 294.4, 294.37, 294.38, 294.38, 294.4, 294.4, 294.37, 294.35, 294.3, 294.3, 294.31, 294.38, 294.43, 294.5, 294.51, 294.56, 294.63, 294.67, 294.65, 294.6, 294.6, 294.6, 294.59, 294.61, 294.58, 294.57, 294.65, 294.58, 294.55, 294.57, 294.48, 294.45, 294.5, 294.58, 294.67, 294.71, 294.73, 294.67, 294.66, 294.61, 294.58, 294.62, 294.57, 294.62, 294.6, 294.54, 294.44, 294.4, 294.36, 294.33, 294.33, 294.28, 294.32, 294.27, 294.26, 294.2, 294.2, 294.23, 294.24, 294.24, 294.24, 294.25, 294.18, 294.19, 294.19, 294.19, 294.19, 294.24, 294.25, 294.35, 294.34, 294.38, 294.4, 294.37, 294.36, 294.44, 294.33, 294.32, 294.38, 294.33, 294.33, 294.31, 294.33, 294.34, 294.35, 294.32, 294.36, 294.37, 294.41, 294.39, 294.33, 294.31, 294.36, 294.4, 294.41, 294.41, 294.39, 294.37, 294.34, 294.35, 294.27, 294.2, 294.21, 294.22, 294.23, 294.25, 294.26, 294.17, 294.17, 294.09, 294.04, 294.09, 294.11, 294.12, 294.06, 294.0, 293.97, 293.99, 293.95, 293.96, 293.97, 293.98, 293.89, 293.92, 293.91, 293.88, 293.87, 293.93, 293.92, 293.93, 293.89, 293.88, 293.91, 293.98, 293.97, 294.0, 293.94, 293.92, 293.91, 293.91, 293.93, 293.86, 293.85, 293.9, 293.88, 293.89, 293.94, 293.89, 293.89, 293.94, 293.97, 294.0, 293.94, 293.91, 293.95, 294.0, 294.04, 293.96, 293.95, 293.88, 293.91, 293.89, 293.88], "current": {"tenkan": 293.991, "kijun": 293.977, "senkou_a": 293.95099999999996, "senkou_b": 294.0195, "chikou": 293.875}, "cloud_signal": "below_cloud"}, "overall_signal": {"signal": "bearish", "confidence": 66.67, "breakdown": {"bullish": 0, "bearish": 2, "neutral": 1}, "recommendation": "sell"}}}