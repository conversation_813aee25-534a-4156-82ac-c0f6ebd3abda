"""
اختبار النظام المستمر لـ PyQuotex
"""

import asyncio
import logging
import json
from pathlib import Path
from data_manager import DataManager
from continuous_trading_system import ContinuousTradingSystem

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_data_manager():
    """اختبار مدير البيانات"""
    print("🧪 اختبار مدير البيانات...")
    
    dm = DataManager()
    
    # اختبار حفظ الأزواج التقليدية
    test_assets = {
        "EURUSD_otc": {
            "name": "EURUSD_otc",
            "is_open": True,
            "payout": 85.5,
            "last_update": "2025-01-22T10:00:00"
        },
        "GBPUSD_otc": {
            "name": "GBPUSD_otc", 
            "is_open": True,
            "payout": 84.2,
            "last_update": "2025-01-22T10:00:00"
        }
    }
    
    dm.save_traditional_assets(test_assets)
    loaded_assets = dm.load_traditional_assets()
    
    assert len(loaded_assets) == 2, "فشل في حفظ/تحميل الأزواج التقليدية"
    print("✅ اختبار الأزواج التقليدية نجح")
    
    # اختبار حفظ البيانات التاريخية
    test_candles = [
        {
            "timestamp": 1642857600,
            "open": 1.1234,
            "high": 1.1245,
            "low": 1.1230,
            "close": 1.1240,
            "volume": 1000
        },
        {
            "timestamp": 1642857900,
            "open": 1.1240,
            "high": 1.1250,
            "low": 1.1235,
            "close": 1.1248,
            "volume": 1200
        }
    ]
    
    dm.save_historical_candles("EURUSD_otc", test_candles, 300)
    loaded_candles = dm.load_historical_candles("EURUSD_otc", 300)
    
    assert len(loaded_candles) == 2, "فشل في حفظ/تحميل البيانات التاريخية"
    print("✅ اختبار البيانات التاريخية نجح")
    
    # اختبار آخر timestamp
    last_timestamp = dm.get_last_candle_timestamp("EURUSD_otc", 300)
    assert last_timestamp == 1642857900, "فشل في الحصول على آخر timestamp"
    print("✅ اختبار آخر timestamp نجح")
    
    print("✅ جميع اختبارات مدير البيانات نجحت!")

async def test_indicators():
    """اختبار المؤشرات التقنية"""
    print("\n🧪 اختبار المؤشرات التقنية...")
    
    from pyquotex.utils.indicators import TechnicalIndicators
    
    indicators = TechnicalIndicators()
    
    # بيانات اختبار
    test_prices = [1.1200, 1.1210, 1.1205, 1.1215, 1.1220, 1.1225, 1.1230, 1.1235, 
                   1.1240, 1.1245, 1.1250, 1.1255, 1.1260, 1.1265, 1.1270, 1.1275,
                   1.1280, 1.1285, 1.1290, 1.1295, 1.1300]
    
    test_highs = [p + 0.0005 for p in test_prices]
    test_lows = [p - 0.0005 for p in test_prices]
    
    # اختبار RSI
    rsi_values = indicators.calculate_rsi(test_prices, 14)
    assert len(rsi_values) > 0, "فشل في حساب RSI"
    print(f"✅ RSI: {rsi_values[-1]:.2f}")
    
    # اختبار MACD
    macd_data = indicators.calculate_macd(test_prices, 12, 26, 9)
    assert 'macd' in macd_data, "فشل في حساب MACD"
    print(f"✅ MACD: {len(macd_data['macd'])} قيمة")
    
    # اختبار المتوسطات المتحركة
    sma_20 = indicators.calculate_sma(test_prices, 20)
    ema_20 = indicators.calculate_ema(test_prices, 20)
    assert len(sma_20) > 0 and len(ema_20) > 0, "فشل في حساب المتوسطات المتحركة"
    print(f"✅ SMA: {sma_20[-1]:.4f}, EMA: {ema_20[-1]:.4f}")
    
    # اختبار نطاقات بولينجر
    bollinger = indicators.calculate_bollinger_bands(test_prices, 20, 2)
    assert 'upper' in bollinger and 'lower' in bollinger, "فشل في حساب نطاقات بولينجر"
    print(f"✅ Bollinger: Upper={bollinger['upper'][-1]:.4f}, Lower={bollinger['lower'][-1]:.4f}")
    
    # اختبار ATR
    atr_values = indicators.calculate_atr(test_highs, test_lows, test_prices, 14)
    assert len(atr_values) > 0, "فشل في حساب ATR"
    print(f"✅ ATR: {atr_values[-1]:.6f}")
    
    print("✅ جميع اختبارات المؤشرات التقنية نجحت!")

def test_file_structure():
    """اختبار هيكل الملفات"""
    print("\n🧪 اختبار هيكل الملفات...")
    
    # التحقق من وجود الملفات الأساسية
    required_files = [
        "app.py",
        "data_manager.py", 
        "continuous_trading_system.py",
        "README_CONTINUOUS_SYSTEM.md"
    ]
    
    for file_name in required_files:
        file_path = Path(file_name)
        assert file_path.exists(), f"الملف المطلوب غير موجود: {file_name}"
        print(f"✅ {file_name}")
    
    # التحقق من مجلد البيانات
    data_dir = Path("data")
    if data_dir.exists():
        subdirs = ["historical", "realtime", "indicators"]
        for subdir in subdirs:
            subdir_path = data_dir / subdir
            if subdir_path.exists():
                print(f"✅ data/{subdir}/")
            else:
                print(f"ℹ️ data/{subdir}/ سيتم إنشاؤه عند التشغيل")
    else:
        print("ℹ️ مجلد data/ سيتم إنشاؤه عند التشغيل")
    
    print("✅ جميع اختبارات هيكل الملفات نجحت!")

def test_configuration():
    """اختبار الإعدادات"""
    print("\n🧪 اختبار الإعدادات...")
    
    try:
        from pyquotex.config import credentials
        email, password = credentials()
        
        assert email and password, "بيانات تسجيل الدخول غير مكتملة"
        print("✅ بيانات تسجيل الدخول موجودة")
        
        # إخفاء كلمة المرور في العرض
        masked_email = email[:3] + "*" * (len(email) - 6) + email[-3:] if len(email) > 6 else "***"
        print(f"✅ البريد الإلكتروني: {masked_email}")
        
    except Exception as e:
        print(f"❌ خطأ في الإعدادات: {e}")
        print("ℹ️ تأكد من إعداد بيانات تسجيل الدخول في pyquotex/config.py")
        return False
    
    print("✅ جميع اختبارات الإعدادات نجحت!")
    return True

async def test_system_integration():
    """اختبار تكامل النظام (بدون اتصال فعلي)"""
    print("\n🧪 اختبار تكامل النظام...")
    
    try:
        # إنشاء نظام التداول المستمر
        system = ContinuousTradingSystem()
        
        # التحقق من الإعدادات
        assert system.timeframe == 300, "الإطار الزمني غير صحيح"
        assert system.historical_candles_count == 500, "عدد الشموع التاريخية غير صحيح"
        
        print("✅ إعدادات النظام صحيحة")
        
        # اختبار دوال المساعدة
        assert system._is_traditional_asset("EURUSD_otc") == True, "فشل في تحديد الأصل التقليدي"
        assert system._is_traditional_asset("BTCUSD") == False, "فشل في استبعاد العملة المشفرة"
        assert system._is_traditional_asset("AAPL") == False, "فشل في استبعاد السهم"
        
        print("✅ دوال تحديد الأصول تعمل بشكل صحيح")
        
        # اختبار دوال الإشارات
        rsi_signal = system._get_rsi_signal(25)
        assert rsi_signal == "oversold", "فشل في تحديد إشارة RSI"
        
        rsi_signal = system._get_rsi_signal(75)
        assert rsi_signal == "overbought", "فشل في تحديد إشارة RSI"
        
        print("✅ دوال الإشارات تعمل بشكل صحيح")
        
        print("✅ جميع اختبارات تكامل النظام نجحت!")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تكامل النظام: {e}")
        return False
    
    return True

async def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبارات النظام المستمر لـ PyQuotex")
    print("=" * 60)
    
    try:
        # اختبار هيكل الملفات
        test_file_structure()
        
        # اختبار الإعدادات
        config_ok = test_configuration()
        
        # اختبار مدير البيانات
        await test_data_manager()
        
        # اختبار المؤشرات التقنية
        await test_indicators()
        
        # اختبار تكامل النظام
        if config_ok:
            await test_system_integration()
        else:
            print("⚠️ تم تخطي اختبار تكامل النظام بسبب مشاكل في الإعدادات")
        
        print("\n" + "=" * 60)
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للتشغيل")
        print("\n📋 للبدء:")
        print("   python app.py start-continuous")
        print("\n📊 لعرض الحالة:")
        print("   python app.py system-status")
        print("\n📈 لعرض بيانات أصل:")
        print("   python app.py show-asset-data --asset EURUSD_otc")
        
    except Exception as e:
        print(f"\n❌ فشل في الاختبارات: {e}")
        print("🔧 راجع الأخطاء وأعد المحاولة")

if __name__ == "__main__":
    asyncio.run(main())
