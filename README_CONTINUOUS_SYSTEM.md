# نظام التداول المستمر لـ PyQuotex

## نظرة عامة

تم تطوير نظام تداول متقدم يعمل 24/7 مع الميزات التالية:

### ✨ الميزات الرئيسية

#### 🔄 الاتصال المستمر
- اتصال مستمر بمنصة Quotex
- إعادة الاتصال التلقائي عند انقطاع الشبكة
- مراقبة صحة الاتصال كل دقيقة
- حد أقصى 10 محاولات إعادة اتصال

#### 📊 إدارة البيانات الذكية
- جلب وحفظ بيانات الملف الشخصي والحسابات
- قائمة الأزواج التقليدية مع نسب الربح المحدثة
- 500 شمعة تاريخية (5 دقائق) لكل زوج
- تحديث ذكي للبيانات (جلب الشموع الجديدة فقط)

#### 📡 البيانات المباشرة
- اشتراك متوازي في جميع الأزواج التقليدية
- تحديث لحظي للشموع المباشرة
- نقل تلقائي للشموع المغلقة إلى البيانات التاريخية
- معالجة متوازية لـ 63+ زوج في نفس الوقت

#### 📈 المؤشرات التقنية الشاملة
- **RSI** (مؤشر القوة النسبية) مع إشارات البيع/الشراء المفرط
- **MACD** (تقارب وتباعد المتوسطات) مع خط الإشارة والهيستوجرام
- **المتوسطات المتحركة** (SMA/EMA 20 و 50) مع تحليل الاتجاه
- **نطاقات بولينجر** مع تحديد موقع السعر
- **المؤشر العشوائي** مع إشارات البيع/الشراء المفرط
- **ATR** (متوسط المدى الحقيقي) لقياس التقلب
- **ADX** (مؤشر الاتجاه المتوسط) لقوة الاتجاه
- **سحابة إيشيموكو** مع تحليل موقع السعر
- **إشارة عامة مجمعة** مع نسبة الثقة والتوصية

## 🚀 كيفية الاستخدام

### 1. بدء النظام المستمر
```bash
python app.py start-continuous
```

هذا الأمر سيقوم بـ:
- الاتصال بمنصة Quotex
- جلب بيانات الملف الشخصي والحسابات
- جلب قائمة الأزواج التقليدية (فوركس فقط)
- جلب 500 شمعة تاريخية لكل زوج
- بدء الاشتراك في البيانات المباشرة
- حساب المؤشرات التقنية بشكل مستمر

### 2. عرض حالة النظام
```bash
python app.py system-status
```

يعرض:
- عدد الأزواج المحفوظة
- إحصائيات البيانات في الكاش
- عينة من الأزواج مع نسب الربح
- عدد ملفات البيانات المحفوظة

### 3. عرض بيانات أصل محدد
```bash
python app.py show-asset-data --asset EURUSD_otc
```

يعرض:
- آخر 5 شموع تاريخية
- الشمعة المباشرة الحالية
- جميع المؤشرات التقنية مع الإشارات
- التوصية العامة

## 📁 هيكل البيانات المحفوظة

```
data/
├── traditional_assets.json          # قائمة الأزواج التقليدية
├── profile_data.json               # بيانات الملف الشخصي
├── historical/                     # البيانات التاريخية
│   ├── EURUSD_otc_300s.json
│   ├── GBPUSD_otc_300s.json
│   └── ...
├── realtime/                       # البيانات المباشرة
│   ├── EURUSD_otc_300s_realtime.json
│   ├── GBPUSD_otc_300s_realtime.json
│   └── ...
└── indicators/                     # المؤشرات التقنية
    ├── EURUSD_otc_300s_indicators.json
    ├── GBPUSD_otc_300s_indicators.json
    └── ...
```

## 🔧 الإعدادات

### إعدادات افتراضية:
- **الإطار الزمني**: 5 دقائق (300 ثانية)
- **عدد الشموع التاريخية**: 500 شمعة
- **تحديث نسب الربح**: كل 5 دقائق
- **تحديث بيانات الملف الشخصي**: كل 30 دقيقة
- **فحص صحة الاتصال**: كل دقيقة

### الأزواج المدعومة:
- **الأزواج التقليدية فقط** (فوركس)
- يستبعد العملات المشفرة والأسهم والمعادن
- يدعم العملات: EUR, USD, GBP, JPY, CHF, CAD, AUD, NZD, SEK, NOK, DKK, PLN, CZK, HUF, TRY, ZAR

## 📊 المؤشرات التقنية المتاحة

### 1. RSI (مؤشر القوة النسبية)
- **الفترة**: 14
- **الإشارات**: 
  - أقل من 30: بيع مفرط (إشارة شراء)
  - أكثر من 70: شراء مفرط (إشارة بيع)

### 2. MACD
- **الإعدادات**: 12, 26, 9
- **المكونات**: خط MACD، خط الإشارة، الهيستوجرام
- **الإشارة**: صاعد/هابط حسب موقع خط MACD من خط الإشارة

### 3. المتوسطات المتحركة
- **SMA/EMA**: 20 و 50 فترة
- **الإشارة**: موقع السعر من المتوسطات

### 4. نطاقات بولينجر
- **الإعدادات**: 20 فترة، 2 انحراف معياري
- **الإشارة**: موقع السعر (فوق/داخل/تحت النطاق)

### 5. المؤشر العشوائي
- **الإعدادات**: K=14, D=3
- **الإشارات**: بيع/شراء مفرط

### 6. ATR (متوسط المدى الحقيقي)
- **الفترة**: 14
- **الاستخدام**: قياس التقلب (منخفض/متوسط/عالي)

### 7. ADX (مؤشر الاتجاه المتوسط)
- **الفترة**: 14
- **الإشارة**: قوة الاتجاه (ضعيف/قوي/قوي جداً)

### 8. سحابة إيشيموكو
- **الإعدادات**: 9, 26, 52
- **الإشارة**: موقع السعر من السحابة

### 9. الإشارة العامة المجمعة
- تجمع إشارات جميع المؤشرات
- تحسب نسبة الثقة
- تقدم توصية نهائية (شراء/بيع/انتظار)

## ⚠️ ملاحظات مهمة

1. **البيانات الحقيقية**: جميع البيانات مأخوذة مباشرة من منصة Quotex
2. **لا توجد بيانات وهمية**: النظام يستخدم البيانات الفعلية فقط
3. **التحديث الذكي**: عند إعادة التشغيل، يجلب الشموع الجديدة فقط
4. **الحفظ التلقائي**: جميع البيانات محفوظة في ملفات JSON
5. **المعالجة المتوازية**: يعالج جميع الأزواج بشكل متوازي
6. **إدارة الذاكرة**: نظام كاش ذكي لتحسين الأداء

## 🛑 إيقاف النظام

لإيقاف النظام المستمر، اضغط `Ctrl+C` في الطرفية. سيتم:
- إيقاف جميع الاشتراكات
- حفظ البيانات الحالية
- إغلاق الاتصالات بأمان
- تنظيف الموارد

## 🔍 استكشاف الأخطاء

### مشاكل الاتصال:
- تحقق من بيانات تسجيل الدخول في `pyquotex/config.py`
- تأكد من اتصال الإنترنت
- راجع ملف السجل `pyquotex.log`

### مشاكل البيانات:
- تحقق من وجود مجلد `data/`
- تأكد من صلاحيات الكتابة
- راجع رسائل الخطأ في الطرفية

### مشاكل الأداء:
- قلل عدد الأزواج المراقبة
- زد فترة التحديث
- راقب استخدام الذاكرة

---

**تم تطوير النظام بواسطة**: نظام PyQuotex المحسن
**الإصدار**: 2.0.0 (نظام مستمر)
**التاريخ**: 2025-01-22
