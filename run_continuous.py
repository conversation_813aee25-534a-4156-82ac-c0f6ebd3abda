#!/usr/bin/env python3
"""
تشغيل سريع للنظام المستمر
"""

import asyncio
import sys
import logging
from pathlib import Path

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('continuous_system.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def check_requirements():
    """التحقق من المتطلبات"""
    try:
        import numpy
        import requests
        import websocket
        import pyfiglet
        from pyquotex.stable_api import Quotex
        return True
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("📦 قم بتثبيت المتطلبات: pip install -r requirements.txt")
        return False

def check_config():
    """التحقق من الإعدادات"""
    try:
        from pyquotex.config import credentials
        email, password = credentials()
        if not email or not password:
            print("❌ بيانات تسجيل الدخول غير مكتملة")
            print("🔧 قم بإعداد البريد الإلكتروني وكلمة المرور في pyquotex/config.py")
            return False
        return True
    except Exception as e:
        print(f"❌ خطأ في الإعدادات: {e}")
        return False

async def main():
    """الدالة الرئيسية"""
    print("🚀 نظام التداول المستمر لـ PyQuotex")
    print("=" * 50)
    
    # التحقق من المتطلبات
    if not check_requirements():
        sys.exit(1)
        
    # التحقق من الإعدادات
    if not check_config():
        sys.exit(1)
    
    try:
        # استيراد النظام
        from continuous_trading_system import ContinuousTradingSystem
        
        # إنشاء وتشغيل النظام
        system = ContinuousTradingSystem()
        
        print("✅ جميع المتطلبات متوفرة")
        print("🔄 بدء النظام المستمر...")
        print("⚠️ للإيقاف اضغط Ctrl+C")
        print("-" * 50)
        
        await system.start_system()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        logger.error(f"خطأ في النظام: {e}")
        print(f"❌ خطأ في النظام: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
